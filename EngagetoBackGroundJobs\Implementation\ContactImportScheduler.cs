﻿using ClosedXML.Excel;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.ContactContracts;
using EngagetoContracts.Services;
using EngagetoContracts.Workflow;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoDatabase.ContactsDatabase.Models;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using EngagetoEntities.Validations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Net;
using System.Net.Mail;

namespace EngagetoBackGroundJobs.Implementation
{
    public class ContactImportScheduler : IContactImportScheduler
    {
        private readonly IBlobStorageService _blobStorageService;
        private readonly IContactRepositoryBase _contactRepository;
        private readonly IConfiguration _configuration;
        private readonly ApplicationDbContext _dbContext;
        private readonly db_aa80b1_whatsappbusinessContext _whatsAppBusinessAPI;
        private readonly IGenericRepository _genericRepository;
        private readonly INodeWorkflowEngineService _nodeWorkflowEngineService;
        private readonly ILogHistoryService _logHistoryService;


        public ContactImportScheduler(
            IBlobStorageService blobStorageService,
            IContactRepositoryBase contactRepository,
            IConfiguration configuration,
            ApplicationDbContext applicationDbContext,
            db_aa80b1_whatsappbusinessContext db_Aa80B1_WhatsappbusinessContext,
            IGenericRepository genericRepository,
            INodeWorkflowEngineService nodeWorkflowEngineService,
            ILogHistoryService logHistoryService
            )
        {
            _blobStorageService = blobStorageService;
            _contactRepository = contactRepository;
            _configuration = configuration;
            _genericRepository = genericRepository;
            _dbContext = applicationDbContext;
            _whatsAppBusinessAPI = db_Aa80B1_WhatsappbusinessContext;
            _nodeWorkflowEngineService = nodeWorkflowEngineService;
            _logHistoryService = logHistoryService;
        }


        public async Task<Guid> ProcessImportAsync(string data, Guid businessId, Guid userId, Guid trackerId, string uploadedFileName)
        {
            var contactImportTracker = await _contactRepository.GetByIdAysnc(trackerId);
            try
            {
                contactImportTracker.status = UploadStatus.Started;
                contactImportTracker.UpdatedAt = DateTime.UtcNow;
                contactImportTracker.UpdatedBy = userId;
                await _contactRepository.UpdateAsysnc(contactImportTracker);

                var contentType = CommonHelper.GetMimeFileType(contactImportTracker.FileName ?? string.Empty);
                var tracker = await _contactRepository.GetByIdAysnc(contactImportTracker.Id);
                using (var stream = new FileStream(data, FileMode.Open, FileAccess.Read))
                using (var workbook = new XLWorkbook(stream))
                {
                    var worksheet = workbook.Worksheet(1);
                    // tracker.TotalCount = worksheet.RowsUsed().Count() - 1;
                    var contactDetails = await ProcessContacts(worksheet, businessId, userId);
                    tracker.TotalCount = contactDetails.Count;
                    var distinctValidContacts = contactDetails.Where(c => c.IsValid).Select(x => x.Contact).DistinctBy(i => i.Contact).ToList();

                    tracker.DistinctCount = distinctValidContacts.Count;

                    var databaseContacts = _contactRepository.Get(businessId);
                    var databaseContactSet = new HashSet<string>(databaseContacts.Select(c => c.Contact));

                    var duplicateContacts = distinctValidContacts.Where(contact => databaseContactSet.Contains(contact.Contact)).ToList();
                    tracker.DuplicateCount = duplicateContacts.Count;
                    var nonDuplicateContacts = distinctValidContacts.Where(contact => !databaseContactSet.Contains(contact.Contact)).ToList();
                    tracker.TotalUploadedCount = nonDuplicateContacts.Count;

                    tracker.InvalidCount = contactDetails.Count(c => !c.IsValid);
                    tracker.status = UploadStatus.InProgress;
                    tracker.Message = "Processing contacts...";
                    tracker.UpdatedAt = DateTime.UtcNow;
                    await _contactRepository.UpdateAsysnc(tracker);

                    // Extract original filename from the uploaded filename
                    string originalFileName = Path.GetFileNameWithoutExtension(contactImportTracker.FileName ?? "contacts");
                    string extension = Path.GetExtension(contactImportTracker.FileName ?? ".xlsx");
                    if (string.IsNullOrEmpty(extension)) extension = ".xlsx";

                    // Create a processed version with the original name
                    string processedFileName = $"{originalFileName}_processed{extension}";

                    var fileStream = SaveToExcel(contactDetails);
                    var user = await _whatsAppBusinessAPI.Users.FirstOrDefaultAsync(m => m.CompanyId == businessId.ToString() && m.Id == userId);

                    if (distinctValidContacts.Any())

                    {                    
                        try
                        {
                            await AddOrUpdateContacts(nonDuplicateContacts, duplicateContacts, businessId);

                            await SendEmailWithAttachment(
                                user.EmailAddress ?? string.Empty,
                                 "Import Status",
                                $"Hi {user.Name},<br/>Imported Contacts Status.<br/><strong>See attached file for details.</strong><br/><br/>Regards,<br/>Engageto",
                                fileStream,
                                processedFileName);
                        }
                        catch (Exception ex) { }
                        await _blobStorageService.UpdateFileAsync(uploadedFileName, fileStream, contentType);
                    }

                    tracker.status = UploadStatus.Completed;
                    tracker.Message = "Successfully processed";
                    tracker.UpdatedAt = DateTime.UtcNow;
                    await _contactRepository.UpdateAsysnc(tracker);
                    Console.WriteLine(" SucessFully Updated.");
                }
                return tracker.Id;
            }
            catch (Exception ex)
            {
                contactImportTracker.status = UploadStatus.Failed;
                contactImportTracker.Message = "Failed";
                await _contactRepository.UpdateAsysnc(contactImportTracker);
                Console.WriteLine("Exception You Got Here.");
                throw;
            }
        }
        private async Task<List<ContactInfo>> ProcessContacts(IXLWorksheet worksheet, Guid businessId, Guid userId)
        {
            var contactDetails = new List<ContactInfo>();
            var countryDetailsList = await _whatsAppBusinessAPI.CountryDetails.ToListAsync();
            var existingTags = _dbContext.Tags.Where(x => x.BusinessId == businessId && x.IsActive).GroupBy(t => t.Tag.ToLower()).Select(g => g.First()).ToDictionary(t => t.Tag.ToLower(), t => t.Id);
            var actualRowCount = worksheet.RowsUsed().Count();
            string code = "";

            for (int row = 2; row <= actualRowCount; row++)
            {
                var contact = new ContactInfo { IsValid = true, Contact = new Contacts() };
                for (int col = 1; col <= worksheet.ColumnCount(); col++)
                {
                    var header = worksheet.Cell(1, col)?.Value.ToString()?.ToLower() ?? string.Empty;
                    var cellValue = worksheet.Cell(row, col)?.Value.ToString().Trim() ?? string.Empty;

                    if (!string.IsNullOrEmpty(header))
                    {
                        switch (header)
                        {
                            case "name":
                                if (!string.IsNullOrEmpty(cellValue))
                                    contact.Contact.Name = cellValue;
                                else
                                {
                                    contact.IsValid = false;
                                    contact.Remark = "Invalid Name";
                                }
                                break;

                            case "countrycode":
                                code = cellValue.StartsWith("+") ? cellValue : $"+{cellValue}";
                                var countryDetail = countryDetailsList.FirstOrDefault(c => c.CountryCode.Equals(code));
                                if (countryDetail != null)
                                    contact.Contact.CountryCode = countryDetail.CountryCode;
                                else
                                {
                                    contact.IsValid = false;
                                    contact.Remark = "Invalid Country Code";
                                }
                                break;

                            case "contact":

                                var countryCodeDetail = countryDetailsList.FirstOrDefault(c => c.CountryCode.Equals(code));
                                try
                                {
                                    var (contacts, isValid) = PhoneNumberValidator.ValidatePhoneNumber(code, cellValue);
                                    contact.Contact.Contact = cellValue;
                                }
                                catch (Exception ex)
                                {
                                    contact.IsValid = false;
                                    contact.Contact.Contact = cellValue;
                                    contact.Remark = "Invalid Contact Number";
                                }
                                break;

                            case "email":
                                if (StringHelper.IsValidEmail(cellValue))
                                    contact.Contact.Email = cellValue;

                                break;
                            case "countryname":
                                countryDetail = countryDetailsList.FirstOrDefault(c => c.CountryName.Equals(cellValue, StringComparison.OrdinalIgnoreCase));
                                if (countryDetail != null)
                                    contact.Contact.CountryName = countryDetail.CountryName;
                                break;
                            case "tags":
                                var tagValues = cellValue.Split(',').Select(tag => tag.Trim()).Distinct().ToList();
                                var tagIds = new List<string>();

                                foreach (var tagValue in tagValues)
                                {
                                    if (existingTags.TryGetValue(tagValue.ToLowerInvariant(), out var tagId))
                                    {
                                        tagIds.Add(tagId.ToString());
                                    }
                                    else
                                    {
                                        var newTag = new EngagetoEntities.Entities.Tags
                                        {
                                            Tag = tagValue,
                                            Id = Guid.NewGuid(),
                                            BusinessId = businessId,
                                            CreatedAt = DateTime.UtcNow,
                                            CreatedBy = userId.ToString(),
                                            UpdatedAt = DateTime.UtcNow,
                                            UpdatedBy = userId.ToString(),
                                            IsActive = true,
                                            UserId = userId
                                        };
                                        await _dbContext.AddAsync(newTag);
                                        await _dbContext.SaveChangesAsync();
                                        existingTags.Add(tagValue.ToLowerInvariant(), newTag.Id);
                                        tagIds.Add(newTag.Id.ToString());
                                    }
                                }
                                contact.Contact.Tags = string.Join(",", tagIds);
                                break;
                        }
                        contact.Contact.Source = SourceType.Excel;
                    }
                    else
                    {
                        break;
                    }
                }
                if (contact.IsValid)
                {
                    contact.Contact.ContactId = Guid.NewGuid();
                    contact.Contact.CreatedDate = DateTime.UtcNow;
                    contact.Contact.IsActive = true;
                    contact.Contact.UserId = userId;
                    contact.Contact.BusinessId = businessId;
                    contact.Contact.ChatStatus = EngagetoEntities.Enums.ChatStatus.New;
                    contact.Contact.IsOptIn = EngagetoEntities.Enums.Is_OptIn.optin;
                    contact.Contact.IsSpam = false;
                    contact.Contact.IsBlock = false;
                    contact.IsValid = true;
                    contact.Remark = "Valid";
                }
                else
                {
                    contact.IsValid = false;
                    contact.Remark = "In Valid (Duplicate Contact info)";
                }
                contactDetails.Add(contact);
            }
            return contactDetails;
        }
        private MemoryStream SaveToExcel(List<ContactInfo> contacts)
        {
            var memoryStream = new MemoryStream();
            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add("Contacts");
                worksheet.Cell(1, 1).Value = "Name";
                worksheet.Cell(1, 2).Value = "CountryCode";
                worksheet.Cell(1, 3).Value = "Contact";
                worksheet.Cell(1, 4).Value = "Email";
                worksheet.Cell(1, 5).Value = "CountryName";
                worksheet.Cell(1, 6).Value = "IsValid";
                worksheet.Cell(1, 7).Value = "Remark";

                for (int i = 0; i < contacts.Count; i++)
                {
                    var contact = contacts[i];

                    worksheet.Cell(i + 2, 1).Value = contact.Contact.Name ?? string.Empty;
                    worksheet.Cell(i + 2, 2).Value = contact.Contact.CountryCode ?? string.Empty;
                    worksheet.Cell(i + 2, 3).Value = contact.Contact.Contact ?? string.Empty;
                    worksheet.Cell(i + 2, 4).Value = contact.Contact.Email ?? string.Empty;
                    worksheet.Cell(i + 2, 5).Value = contact.Contact.CountryName ?? string.Empty;
                    worksheet.Cell(i + 2, 6).Value = contact.IsValid ? "Valid" : "Invalid";
                    worksheet.Cell(i + 2, 7).Value = contact.Remark ?? string.Empty;
                }

                workbook.SaveAs(memoryStream);
            }
            memoryStream.Position = 0;
            return memoryStream;
        }

        private async Task AddOrUpdateContacts(List<Contacts> newContacts, List<Contacts> existingContacts, Guid businessId)
        {
            if (newContacts != null && newContacts.Any())
            {
                const int batchSize = 50;
                var newContactList = newContacts
                    .Where(c => !_dbContext.Contacts
                        .Any(dbContact => dbContact.BusinessId == businessId && dbContact.CountryCode == c.CountryCode && dbContact.Contact == c.Contact))
                    .ToList();

                    for (int i = 0; i < newContacts.Count; i += batchSize)
                    {
                        var batch = newContacts.Skip(i).Take(batchSize).ToList();
                        var result = await _genericRepository.InsertRecordsAsync("Contacts", StringHelper.GetPropertyNames<Contacts>(), batch);
                    }
                    await TriggerWorkflowsForNewContactsAsync(newContactList);           
            }

            if (existingContacts != null && existingContacts.Any())
            {
                var contactNumbers = existingContacts.Select(x => string.Concat(x.CountryCode.Replace("+", ""), x.Contact)).ToList();

                var existingContactList = await _dbContext.Contacts
                    .Where(x => x.BusinessId == businessId && contactNumbers.Contains(string.Concat(x.CountryCode.Replace("+", ""), x.Contact)))
                    .ToDictionaryAsync(
                        x => x.ContactId,
                        x => string.Concat(x.CountryCode.Replace("+", ""), x.Contact)
                    );
                foreach (var contact in existingContacts)
                {
                    var number = string.Concat(contact.CountryCode.Replace("+", ""), contact.Contact);
                    var contId = existingContactList.FirstOrDefault(x => x.Value == number);
                    contact.ContactId = contId.Key;
                }
                _dbContext.ChangeTracker.Clear();
                _dbContext.Contacts.UpdateRange(existingContacts);
                await _dbContext.SaveChangesAsync();
            }
        }

        private async Task SendEmailWithAttachment(string toEmail, string subject, string body, MemoryStream attachmentStream, string fileName)
        {
            string fromMail = _configuration["SmtpSettings:SmtpUsername"] ?? string.Empty;
            string fromPassword = _configuration["SmtpSettings:SmtpPassword"] ?? string.Empty;

            MailMessage message = new MailMessage();
            message.From = new MailAddress(fromMail);
            message.Subject = subject;
            message.To.Add(new MailAddress(toEmail));
            message.Body = body;
            message.IsBodyHtml = true;

            attachmentStream.Position = 0;
            Attachment attachment = new Attachment(attachmentStream, fileName, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            message.Attachments.Add(attachment);

            var smtpClient = new SmtpClient(_configuration["SmtpSettings:SmtpServer"])
            {
                Port = Convert.ToInt32(_configuration["SmtpSettings:SmtpPort"]),
                Credentials = new NetworkCredential(fromMail, fromPassword),
                EnableSsl = true,
            };

            await smtpClient.SendMailAsync(message);
        }

        private async Task TriggerWorkflowsForNewContactsAsync(List<Contacts> newContacts)
        {
            try
            {
                var workflowNodes = await _dbContext.WorkflowNodes
                    .Include(n => n.Workflow)
                    .Where(n => n.Type == NodeType.FlowStart &&
                               n.Workflow.IsActive &&
                               !n.Workflow.IsDeleted &&
                               n.Workflow.CompanyId == newContacts.First().BusinessId.ToString())
                    .ToListAsync();

                var hasExcelWorkflow = workflowNodes.Any(n =>
                    n.PayloadModel?.FlowStartModel?.EntryNodeType == EntryNodeType.OnNewLead &&
                    n.PayloadModel?.FlowStartModel?.LeadSource != null &&
                    n.PayloadModel.FlowStartModel.LeadSource.Any(ls => ls.Source == SourceType.Excel.ToString()));

                if (!hasExcelWorkflow) return;

                const int workflowBatchSize = 10;

                for (int i = 0; i < newContacts.Count; i += workflowBatchSize)
                {
                    var batch = newContacts.Skip(i).Take(workflowBatchSize).ToList();

                    foreach (var contact in batch)
                    {
                        try
                        {
                            await _nodeWorkflowEngineService.ProcessWorkflowAsync(contact, null, true);
                        }
                        catch (Exception ex)
                        {
                            await _logHistoryService.SaveErrorLogHistoryAsyn("ProcessWorkflowFromExcelAsync->1", null, $"Processing contacts: {contact.ContactId}", ex.Message, ex.StackTrace);
                        }
                    }
                    await Task.Delay(500);
                }
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveErrorLogHistoryAsyn("ProcessWorkflowFromExcelAsync->2", null, $"Processing contacts", ex.Message, ex.StackTrace);
            }
        }

    }
}
