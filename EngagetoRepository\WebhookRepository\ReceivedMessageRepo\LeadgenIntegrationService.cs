﻿using EngagetoContracts.Services;
using EngagetoContracts.WebhookContracts.ReceivedNotification;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.IntegrationDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text.Json;

namespace EngagetoRepository.WebhookRepository.ReceivedMessageRepo
{
    public class LeadgenIntegrationService : ILeadgenIntegrationService
    {
        private readonly IGenericRepository _genericRepository;
        private readonly ILogger<LeadgenIntegrationService> _logger;
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogHistoryService _logHistoryService;
        private readonly IIntegrationAccountService _integrationAcctount;
        JsonSerializerOptions options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };
        public LeadgenIntegrationService(IGenericRepository genericRepository,
            ApplicationDbContext dbContext,
            ILogHistoryService logHistoryService,
            IIntegrationAccountService integrationAcctount,
            ILogger<LeadgenIntegrationService> logger)
        {
            _genericRepository = genericRepository;
            _integrationAcctount = integrationAcctount;
            _dbContext = dbContext;
            _logHistoryService = logHistoryService;
            _logger = logger;
        }

        public async Task<bool> ProcessIntegrationForLeadAsync(IntegrationAccountRecord record)
        {
            try
            {
                var integrationAccounts = await _genericRepository.GetByObjectAsync<IntegrationAccount>(new Dictionary<string, object>()
                {
                    { "BusinessId", record.businessId },
                    { "IsActive", true },
                    { "IsDeleted", false }
                });

                if (integrationAccounts?.Any() == true)
                {
                    var account = integrationAccounts?
                        .Where(a => a.Action == IntegrationAction.CallByExternal && a.IntegrationEvents != null)
                        .FirstOrDefault(a =>
                            record?.events != null &&
                            a.IntegrationEvents != null &&
                            a.IntegrationEvents.Intersect(record.events).Any()
                        );

                    var events = account?.IntegrationEvents;
                    if (account != null)
                    {
                        await _logHistoryService.SaveInformationLogHistoryAsyn("ProcessIntegrationForLeadAsync", JsonConvert.SerializeObject(record.contact), JsonConvert.SerializeObject(account), $"Pre{JsonConvert.SerializeObject(record.@events)}");

                        if (record.events != null || record.events?.Count > 0)
                        {
                            events = events?.Where(x => record.events.Contains(x)).ToList();
                        }

                        foreach (var @event in events ?? new())
                        {
                            switch (@event)
                            {
                                case IntegrationEvent.OneTimeLeadGen:
                                    await ProcessOneTimeLeadGenEventAsync(record, account, @event);
                                    break;
                                case IntegrationEvent.AutoReply:
                                    await ProcessAutoReplyMessageEventAsync(record, account, @event);
                                    break;
                                case IntegrationEvent.LeadGen:
                                    await ProcessLeadGenEventAsync(record, account, @event);
                                    break;
                                case IntegrationEvent.Template:
                                    await ProcessTemplateEventAsync(record, account, @event);
                                    break;
                                case IntegrationEvent.Received:
                                    await ProcessRecievedLeadGenEventAsync(record, account, @event);
                                    break;
                                case IntegrationEvent.Delivered:
                                    await ProcessDeliveredMessageEventAsync(record, account, @event);
                                    break;
                            }
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveInformationLogHistoryAsyn("Error:ProcessIntegrationForLeadAsync", JsonConvert.SerializeObject(record.contact), JsonConvert.SerializeObject(record), "After");
                return false;
            }
        }

        private async Task ProcessOneTimeLeadGenEventAsync(IntegrationAccountRecord record, IntegrationAccount account, IntegrationEvent @event)
        {

            if (record.contact != null && record.conversation?.To.ToLower() == account.BusinessId.ToLower())
            {
                var contact = record.contact;
                string contactNumber = string.Concat(contact.CountryCode, contact.Contact).Replace("+", "");

                bool hasSentFewerThanTwoMessages = _dbContext.Conversations
                    .Where(x => x.From.Replace("+", "") == contactNumber && x.To.ToLower() == record.businessId.ToLower())
                    .Count() < 2;

                await _logHistoryService.SaveInformationLogHistoryAsyn("ProcessIntegrationForLeadAsync", JsonConvert.SerializeObject(record.contact), JsonConvert.SerializeObject(record.conversation), $"Init:{hasSentFewerThanTwoMessages}");

                if (!hasSentFewerThanTwoMessages) return;

                foreach (var model in account?.PayloadDict ?? new())
                {
                    object? payload = model.Key switch
                    {
                        ModelMapping.Contact => record.contact,
                        ModelMapping.Conversation => record.conversation,
                        ModelMapping.IntegrationPayload => record.integration ?? new IntegrationPayloadForExternal
                        {
                            Contact = record.contact,
                            Conversations = record.conversation
                        },
                        _ => null
                    };

                    if (payload != null)
                    {
                        await _logHistoryService.SaveInformationLogHistoryAsyn(
                            "ProcessIntegrationForLeadAsync",
                            JsonConvert.SerializeObject(record.contact),
                            JsonConvert.SerializeObject(record.conversation),
                            $"After:{hasSentFewerThanTwoMessages}");

                        await _integrationAcctount.CallExternalApiForLeadAsync(
                            record.businessId,
                            JObject.FromObject(payload),
                            model.Key,
                            @event);
                    }
                }
            }

        }

        private async Task ProcessAutoReplyMessageEventAsync(IntegrationAccountRecord record, IntegrationAccount account, IntegrationEvent @event)
        {
            if (record.contact == null || record.conversation == null) return;
            var contact = record.contact;
            string contactNumber = string.Concat(contact.CountryCode, contact.Contact).Replace("+", "");

            bool isAutoReplyMessage = !string.IsNullOrEmpty(record.conversation.ReplyId) ? true : false;
            if (isAutoReplyMessage)
            {
                foreach (var model in account?.PayloadDict ?? new())
                {
                    object? payload = model.Key switch
                    {
                        ModelMapping.Contact => record.contact,
                        ModelMapping.Conversation => record.conversation,
                        ModelMapping.IntegrationPayload => record.integration ?? new IntegrationPayloadForExternal
                        {
                            Contact = record.contact,
                            Conversations = record.conversation
                        },
                        _ => null
                    };

                    if (payload != null)
                    {
                        await _logHistoryService.SaveInformationLogHistoryAsyn(
                            "ProcessAutoReplyMessageEventAsync",
                            JsonConvert.SerializeObject(record.contact),
                            JsonConvert.SerializeObject(record.conversation),
                            $"After:{isAutoReplyMessage}");

                        await _integrationAcctount.CallExternalApiForLeadAsync(
                            record.businessId,
                            JObject.FromObject(payload),
                            model.Key, @event);
                    }
                }
            }
        }

        private async Task ProcessLeadGenEventAsync(IntegrationAccountRecord record, IntegrationAccount account, IntegrationEvent @event)
        {
            if (record.contact == null) return;

            var contact = record.contact;
            string contactNumber = string.Concat(contact.CountryCode, contact.Contact).Replace("+", "");

            bool hasSentFewerThanTwoMessages = _dbContext.Conversations
                .Where(x => x.From.Replace("+", "") == contactNumber && x.To.ToLower() == record.businessId.ToLower())
                .Count() < 2;

            await _logHistoryService.SaveInformationLogHistoryAsyn("ProcessLeadGenEventAsync", JsonConvert.SerializeObject(record.contact), JsonConvert.SerializeObject(record.conversation), $"Init:{hasSentFewerThanTwoMessages}");

            if (!hasSentFewerThanTwoMessages) return;

            foreach (var model in account?.PayloadDict ?? new())
            {
                object? payload = model.Key switch
                {
                    ModelMapping.Contact => record.contact,
                    ModelMapping.Conversation => record.conversation,
                    ModelMapping.IntegrationPayload => record.integration ?? new IntegrationPayloadForExternal
                    {
                        Contact = record.contact,
                        Conversations = record.conversation
                    },
                    _ => null
                };

                if (payload != null)
                {
                    await _logHistoryService.SaveInformationLogHistoryAsyn(
                        "ProcessLeadGenEventAsync",
                        JsonConvert.SerializeObject(record.contact),
                        JsonConvert.SerializeObject(record.conversation),
                        $"After");

                    await _integrationAcctount.CallExternalApiForLeadAsync(
                        record.businessId,
                        JObject.FromObject(payload),
                        model.Key, @event);
                }
            }
        }

        private async Task ProcessRecievedLeadGenEventAsync(IntegrationAccountRecord record, IntegrationAccount account, IntegrationEvent @event)
        {
            if (record.contact == null) return;

            var contact = record.contact;
            string contactNumber = string.Concat(contact.CountryCode, contact.Contact).Replace("+", "");

            await _logHistoryService.SaveInformationLogHistoryAsyn("ProcessRecievedLeadGenEventAsync", JsonConvert.SerializeObject(record.contact), JsonConvert.SerializeObject(record.conversation), $"Event: Recieved lead generation ");

            foreach (var model in account?.PayloadDict ?? new())
            {
                object? payload = model.Key switch
                {
                    ModelMapping.Contact => record.contact,
                    ModelMapping.Conversation => record.conversation,
                    ModelMapping.IntegrationPayload => record.integration ?? new IntegrationPayloadForExternal
                    {
                        Contact = record.contact,
                        Conversations = record.conversation
                    },
                    _ => null
                };

                if (payload != null)
                {
                    await _logHistoryService.SaveInformationLogHistoryAsyn(
                        "ProcessRecievedLeadGenEventAsync",
                        JsonConvert.SerializeObject(record.contact),
                        JsonConvert.SerializeObject(record.conversation),
                        $"After");

                    await _integrationAcctount.CallExternalApiForLeadAsync(
                        record.businessId,
                        JObject.FromObject(payload),
                        model.Key, @event);
                }
            }
        }

        private async Task ProcessDeliveredMessageEventAsync(IntegrationAccountRecord record, IntegrationAccount account, IntegrationEvent @event)
        {
            var existContact = record.conversation != null
                               ? _dbContext.Contacts.Where(x => x.BusinessId.ToString().ToLower() == record.businessId.ToLower()
                               && (x.CountryCode + x.Contact).Replace("+", "") == (record.conversation.To).Replace("+", "")).FirstOrDefault()
                               : null;
            var existConversation = record.conversation;
            record = record with { contact = existContact };

            if (existConversation != null && record.conversation != null)
                existConversation.TextMessage = record.conversation.TextMessage ?? record.conversation.TemplateBody ?? (record.conversation.MediaUrl + "/n" + record.conversation.MediaCaption);

            record = record with { conversation = existConversation };

            _logger.LogInformation("ProcessDeliveredLeadNotesEventAsync | Event: Delivered message update in lead notes | Contact: {ContactJson} | Conversation: {ConversationJson}",
                JsonConvert.SerializeObject(record.contact),
                JsonConvert.SerializeObject(record.conversation));

            foreach (var model in account?.PayloadDict ?? new())
            {
                object? payload = model.Key switch
                {
                    ModelMapping.Contact => record.contact,
                    ModelMapping.Conversation => record.conversation,
                    ModelMapping.IntegrationPayload => record.integration ?? new IntegrationPayloadForExternal
                    {
                        Contact = record.contact,
                        Conversations = record.conversation
                    },
                    _ => null
                };

                if (payload != null)
                {
                    _logger.LogInformation("After : ProcessDeliveredLeadNotesEventAsync Processing | Contact: {ContactJson} | Conversation: {ConversationJson}",
                                   JsonConvert.SerializeObject(record.contact),
                                   JsonConvert.SerializeObject(record.conversation));

                    await _integrationAcctount.CallExternalApiForLeadAsync(
                        record.businessId,
                        JObject.FromObject(payload),
                        model.Key, @event);
                }
            }
        }

        private async Task ProcessTemplateEventAsync(IntegrationAccountRecord record, IntegrationAccount account, IntegrationEvent @event)
        {
            try
            {
                if (!(await _genericRepository.IsExistAsync<CampaignTracker>(new() { { "WhatsAppMessagesId", record?.conversation?.WhatsAppMessageId ?? string.Empty } }, "CampaignTracker")))
                {
                    var buttons = await _genericRepository.GetByObjectAsync<Button>(new Dictionary<string, object>() { { "TemplateId", record.template?.TemplateId ?? Guid.Empty } }, "ButtonDetails");
                    if (buttons?.Any() == true)
                    {
                        record.template.Buttons = buttons;
                    }
                    await _logHistoryService.SaveInformationLogHistoryAsyn("ProcessTemplateEventAsync", JsonConvert.SerializeObject(record.template), null, $"Init");
                    foreach (var model in account?.PayloadDict ?? new())
                    {
                        object? payload = model.Key switch
                        {
                            ModelMapping.IntegrationPayload => record.integration ?? new IntegrationPayloadForExternal
                            {
                                Template = record.template
                            },
                            _ => null
                        };

                        if (payload != null)
                        {
                            await _logHistoryService.SaveInformationLogHistoryAsyn(
                                "ProcessTemplateEventAsync",
                                JsonConvert.SerializeObject(record.template),
                                null,
                                $"After");

                            await _integrationAcctount.CallExternalApiForLeadAsync(
                                record.businessId,
                                JObject.FromObject(payload),
                                model.Key, @event);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                await _logHistoryService.SaveInformationLogHistoryAsyn(
                           "ProcessTemplateEventAsyncError",
                           JsonConvert.SerializeObject(record.template),
                           null,
                           $"Error");
            }
        }
    }
}

