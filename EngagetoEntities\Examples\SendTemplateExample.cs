using EngagetoEntities.Dtos.TemplateDtos;

namespace EngagetoEntities.Examples
{
    /// <summary>
    /// Example demonstrating how to use the updated SendTemplateDto with both normal and LeadRat variables
    /// </summary>
    public static class SendTemplateExample
    {
        /// <summary>
        /// Example 1: Template with normal variables only
        /// Template body: "Hello {{1}}, your appointment is on {{2}}"
        /// </summary>
        public static SendTemplateDto CreateNormalVariableExample()
        {
            return new SendTemplateDto
            {
                BusinessId = "your-business-id",
                UserId = Guid.NewGuid(),
                TemplateId = Guid.NewGuid(),
                Contact = new List<string> { "+1234567890" },
                BodyVariableValues = new List<VariableDto>
                {
                    new VariableDto
                    {
                        Value = "Name", // Contact property name to fetch from database
                        FallbackValue = "Customer"
                    },
                    new VariableDto
                    {
                        Value = "ScheduleDate", // Contact property name
                        FallbackValue = "Soon"
                    }
                }
            };
        }

        /// <summary>
        /// Example 2: Template with LeadRat variables only
        /// Template body: "Hello #leadName#, your property #propertyName# is ready"
        /// Variables are auto-extracted from template, only fallback values needed
        /// </summary>
        public static SendTemplateDto CreateLeadRatVariableExample()
        {
            return new SendTemplateDto
            {
                BusinessId = "your-business-id",
                UserId = Guid.NewGuid(),
                TemplateId = Guid.NewGuid(),
                Contact = new List<string> { "+1234567890" },
                BodyVariableValues = new List<VariableDto>
                {
                    new VariableDto
                    {
                        Value = null, // Not needed for LeadRat variables
                        FallbackValue = "Customer" // Fallback for #leadName#
                    }
                    // Note: Only one VariableDto needed as fallback applies to all LeadRat variables
                }
            };
        }

        /// <summary>
        /// Example 3: Template with mixed variables - NOT RECOMMENDED
        /// Templates should use either normal variables OR LeadRat variables, not both
        /// Template body: "Hello {{1}}, your appointment is on {{2}}" (normal variables only)
        /// </summary>
        public static SendTemplateDto CreateMixedVariableExample()
        {
            return new SendTemplateDto
            {
                BusinessId = "your-business-id",
                UserId = Guid.NewGuid(),
                TemplateId = Guid.NewGuid(),
                Contact = new List<string> { "+1234567890" },
                HeaderVariableValue = new VariableDto
                {
                    Value = "Name", // Normal variable - fetch from contact database
                    FallbackValue = "Customer"
                },
                BodyVariableValues = new List<VariableDto>
                {
                    new VariableDto
                    {
                        Value = "Name", // Normal variable {{1}}
                        FallbackValue = "Customer"
                    },
                    new VariableDto
                    {
                        Value = "ScheduleDate", // Normal variable {{2}}
                        FallbackValue = "Soon"
                    }
                }
            };
        }

        /// <summary>
        /// Example 4: Template with header variable
        /// Template header: "{{1}}" (normal variable)
        /// Template body: "Your property details" (no variables)
        /// </summary>
        public static SendTemplateDto CreateHeaderVariableExample()
        {
            return new SendTemplateDto
            {
                BusinessId = "your-business-id",
                UserId = Guid.NewGuid(),
                TemplateId = Guid.NewGuid(),
                Contact = new List<string> { "+1234567890" },
                HeaderVariableValue = new VariableDto
                {
                    Value = "Name", // Normal variable for header
                    FallbackValue = "Customer"
                }
                // No BodyVariableValues needed if body has no variables
            };
        }
    }
}
