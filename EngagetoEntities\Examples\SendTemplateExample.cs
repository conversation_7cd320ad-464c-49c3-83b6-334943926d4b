using EngagetoEntities.Dtos.TemplateDtos;

namespace EngagetoEntities.Examples
{
    /// <summary>
    /// Example demonstrating how to use the updated SendTemplateDto with both normal and LeadRat variables
    /// </summary>
    public static class SendTemplateExample
    {
        /// <summary>
        /// Example 1: Template with normal variables only
        /// Template body: "Hello {{1}}, your appointment is on {{2}}"
        /// </summary>
        public static SendTemplateDto CreateNormalVariableExample()
        {
            return new SendTemplateDto
            {
                BusinessId = "your-business-id",
                UserId = Guid.NewGuid(),
                TemplateId = Guid.NewGuid(),
                Contact = new List<string> { "+1234567890" },
                BodyVariableValues = new List<VariableDto>
                {
                    new VariableDto
                    {
                        Variable = null, // Not needed for normal variables
                        Value = "Name", // Contact property name to fetch from database
                        FallbackValue = "Customer"
                    },
                    new VariableDto
                    {
                        Variable = null,
                        Value = "ScheduleDate", // Contact property name
                        FallbackValue = "Soon"
                    }
                }
            };
        }

        /// <summary>
        /// Example 2: Template with LeadRat variables only
        /// Template body: "Hello #leadName#, your property #propertyName# is ready"
        /// </summary>
        public static SendTemplateDto CreateLeadRatVariableExample()
        {
            return new SendTemplateDto
            {
                BusinessId = "your-business-id",
                UserId = Guid.NewGuid(),
                TemplateId = Guid.NewGuid(),
                Contact = new List<string> { "+1234567890" },
                BodyVariableValues = new List<VariableDto>
                {
                    new VariableDto
                    {
                        Variable = "#leadName#", // LeadRat variable
                        Value = null, // Not needed, will be fetched from LeadRat API
                        FallbackValue = "Customer"
                    },
                    new VariableDto
                    {
                        Variable = "#propertyName#", // LeadRat variable
                        Value = null,
                        FallbackValue = "Property"
                    }
                }
            };
        }

        /// <summary>
        /// Example 3: Template with mixed variables
        /// Template body: "Hello {{1}}, your LeadRat property #propertyName# appointment is on {{2}}"
        /// </summary>
        public static SendTemplateDto CreateMixedVariableExample()
        {
            return new SendTemplateDto
            {
                BusinessId = "your-business-id",
                UserId = Guid.NewGuid(),
                TemplateId = Guid.NewGuid(),
                Contact = new List<string> { "+1234567890" },
                HeaderVariableValue = new VariableDto
                {
                    Variable = null,
                    Value = "Name", // Normal variable - fetch from contact database
                    FallbackValue = "Customer"
                },
                BodyVariableValues = new List<VariableDto>
                {
                    new VariableDto
                    {
                        Variable = null,
                        Value = "Name", // Normal variable {{1}}
                        FallbackValue = "Customer"
                    },
                    new VariableDto
                    {
                        Variable = "#propertyName#", // LeadRat variable
                        Value = null,
                        FallbackValue = "Property"
                    },
                    new VariableDto
                    {
                        Variable = null,
                        Value = "ScheduleDate", // Normal variable {{2}}
                        FallbackValue = "Soon"
                    }
                }
            };
        }

        /// <summary>
        /// Example 4: Template with header variable
        /// Template header: "{{1}}" (normal variable)
        /// Template body: "Your property #propertyName# details" (LeadRat variable)
        /// </summary>
        public static SendTemplateDto CreateHeaderVariableExample()
        {
            return new SendTemplateDto
            {
                BusinessId = "your-business-id",
                UserId = Guid.NewGuid(),
                TemplateId = Guid.NewGuid(),
                Contact = new List<string> { "+1234567890" },
                HeaderVariableValue = new VariableDto
                {
                    Variable = null,
                    Value = "Name", // Normal variable for header
                    FallbackValue = "Customer"
                },
                BodyVariableValues = new List<VariableDto>
                {
                    new VariableDto
                    {
                        Variable = "#propertyName#", // LeadRat variable for body
                        Value = null,
                        FallbackValue = "Property"
                    }
                }
            };
        }
    }
}
