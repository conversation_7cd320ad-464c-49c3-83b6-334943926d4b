-- =============================================
-- Author: Augment Agent
-- Create date: 2025-07-07
-- Description: Get campaign contacts filtered by status with pagination
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[GetCampaignContactsByStatus]
    @CampaignId UNIQUEIDENTIFIER,
    @Status INT,
    @BusinessId NVARCHAR(50) = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 10
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Validate inputs
    IF @PageNumber < 1 SET @PageNumber = 1;
    IF @PageSize < 1 SET @PageSize = 10;
    IF @PageSize > 100 SET @PageSize = 100; -- Limit max page size
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    -- Main query with pagination
    WITH CampaignContacts AS (
        SELECT DISTINCT
            c.ContactId,
            c.<PERSON>,
            c.<PERSON>,
            c.<PERSON>Code,
            c.Email,
            c.Tags,
            c.<PERSON>t<PERSON>tat<PERSON>,
            c.CreatedDate,
            c.IsActive,
            conv.Status as ConversationStatus,
            conv.CreatedAt as ConversationCreatedAt,
            conv.WhatsAppMessageId,
            conv.TextMessage,
            conv.ErrorMessage,
            ROW_NUMBER() OVER (ORDER BY conv.CreatedAt DESC) as RowNum
        FROM Conversations conv WITH (NOLOCK)
        INNER JOIN Contacts c WITH (NOLOCK) 
            ON REPLACE(conv.[To], '+', '') = REPLACE(c.CountryCode + c.Contact, '+', '')
        WHERE conv.ReferenceId = CAST(@CampaignId AS NVARCHAR(36))
        AND conv.Status = @Status
        AND (@BusinessId IS NULL OR c.BusinessId = @BusinessId)
    ),
    TotalCountCTE AS (
        SELECT COUNT(*) as TotalCount FROM CampaignContacts
    )
    SELECT 
        cc.ContactId,
        cc.Name,
        cc.Contact,
        cc.CountryCode,
        cc.Email,
        cc.Tags,
        cc.ChatStatus,
        cc.CreatedDate,
        cc.IsActive,
        cc.ConversationStatus,
        cc.ConversationCreatedAt,
        cc.WhatsAppMessageId,
        cc.TextMessage,
        cc.ErrorMessage,
        tc.TotalCount
    FROM CampaignContacts cc
    CROSS JOIN TotalCountCTE tc
    WHERE cc.RowNum BETWEEN @Offset + 1 AND @Offset + @PageSize
    ORDER BY cc.ConversationCreatedAt DESC;
    
    -- Get status summary counts
    SELECT 
        conv.Status,
        COUNT(*) as Count
    FROM Conversations conv WITH (NOLOCK)
    INNER JOIN Contacts c WITH (NOLOCK) 
        ON REPLACE(conv.[To], '+', '') = REPLACE(c.CountryCode + c.Contact, '+', '')
    WHERE conv.ReferenceId = CAST(@CampaignId AS NVARCHAR(36))
    AND (@BusinessId IS NULL OR c.BusinessId = @BusinessId)
    GROUP BY conv.Status;
END
