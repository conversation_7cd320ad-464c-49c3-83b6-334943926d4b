﻿using EngagetoContracts.GeneralContracts;
using EngagetoContracts.Services;
using EngagetoDapper.Data.Dapper.Repositories.InboxRepositories;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.CampaignDto;
using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Dtos.FilterDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Response;
using EngagetoEntities.UserEntities.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Text;
using System.Text.Json;

namespace Engageto.Controllers.CampaignControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CampaignsController : ControllerBase
    {

        private readonly ICampaignService _campaignService;
        private readonly IUserIdentityService _userIdentityService;
        private ApplicationDbContext _CompaignDbContext;
        private readonly EngagetoDapper.Data.Dapper.Repositories.InboxRepositories.IInboxRepository _inboxRepository;
        private readonly IConfiguration _configuration;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly string _campaignSchedulerJobLink;
        private readonly IEnvironmentService _environmentService;


        public CampaignsController(ApplicationDbContext compaignDbContext, ICampaignService campaignService, IUserIdentityService userIdentityService, IInboxRepository inboxRepository,
            IConfiguration configuration, IHttpClientFactory httpClientFactory, IEnvironmentService environmentService)
        {
            _campaignService = campaignService;
            _userIdentityService = userIdentityService;
            _CompaignDbContext = compaignDbContext;
            _inboxRepository = inboxRepository;
            _configuration = configuration;
            _httpClientFactory = httpClientFactory;
            _environmentService = environmentService;


            if (_environmentService.IsDevelopment)
                _campaignSchedulerJobLink = _configuration["FunctionSettings:Dev_ScheduleCampaignUrl"] ?? "";
            else
                _campaignSchedulerJobLink = _configuration["FunctionSettings:Prod_ScheduleCampaignUrl"] ?? "";
        }

        [HttpPost("scheduleCampaignRequest")]
        [Authorize]
        public async Task<IActionResult> ScheduleCampaign([FromBody] InputPayloadDto payload)
        {
            var functionUrl = _campaignSchedulerJobLink;
            if (string.IsNullOrEmpty(functionUrl))
            {
                return BadRequest("Function URL is not configured.");
            }

            var client = _httpClientFactory.CreateClient();
            var jsonPayload = JsonSerializer.Serialize(payload);
            var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

            var response = await client.PostAsync(functionUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            return Ok(new
            {
                statusCode = response.StatusCode,
                response = result
            });
        }

        [HttpPost("campaign")]
        [HttpPut("campaign")]
        [Authorize]
        public async Task<IActionResult> SaveCampaign(CreateCampaignDto dto)
        {
            try
            {
                if (_userIdentityService.BusinessId != dto.BusinessId) throw new UnauthorizedAccessException("Invalid user");
                var result = await _campaignService.CreateAsync(_userIdentityService.UserId, _userIdentityService.Name, dto);
                return Ok(new ApiResponse<string>
                {
                    Message = "created Successfull.",
                    Success = true
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpPost("rerunCampaign")]
        [Authorize]
        public async Task<IActionResult> RerunCampaign(BaseCampaignsDto campaign, DateTime? scheduledDate)
        {
            try
            {
                if (_userIdentityService.BusinessId != campaign.BusinessId) throw new UnauthorizedAccessException("Invalid user");
                var result = await _campaignService.RerunCampaignAsync(_userIdentityService.UserId, _userIdentityService.Name, campaign, scheduledDate);
                return Ok(new ApiResponse<string>
                {
                    Message = "Rerun campaign process has been Successfully completed.",
                    Success = true
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpGet("exist-campaign/{businessId}/{name}")]
        [Authorize]
        public async Task<IActionResult> ExistCampaignName(string businessId, string name)
        {
            try
            {
                if (_userIdentityService.BusinessId != businessId) throw new UnauthorizedAccessException("Invalid user");
                var result = await _campaignService.IsExistCampaignNameAsync(businessId, name);
                return Ok(new ApiResponse<string>()
                {
                    Success = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpDelete("campaign/{id}")]
        [Authorize]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _campaignService.DeleteAsync(_userIdentityService.BusinessId, id);
                return Ok(new ApiResponse<string>
                {
                    Message = "Deleted Successfull.",
                    Success = true
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
        /// <summary>
        /// Handles the request to retrieve campaign analytics based on the provided filter criteria.
        /// </summary>
        /// <param name="filter">The filter criteria to apply when retrieving campaign analytics.</param>
        /// <returns>
        /// Returns an IActionResult:
        /// - 200 OK with a list of campaign message counts if the operation is successful.
        /// - 400 BadRequest with error details if an exception occurs.
        /// </returns>
        [HttpPost("campaign-analytics")]
        [Authorize]
        public async Task<IActionResult> GetCampaignAnalytics(FilterDto? filter, [FromQuery] int? page, [FromQuery] int? pageSize)
        {
            try
            {
                var result = await _campaignService.GetCampaignAnalyticsAsync(_userIdentityService.BusinessId, _userIdentityService.UserId, filter, page, pageSize);
                return Ok(new ApiResponse<List<CampaignMessageCountsDto>>
                {
                    Message = "Get campaign analytics",
                    Success = true,
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpPost("CampaignAllAnalytics")]
        [Authorize]
        public async Task<IActionResult> GetAllCampaignAnalytics(FilterDto? filter, [FromQuery] int? page, [FromQuery] int? pageSize)
        {
            try
            {
                var result = await _campaignService.GetAllCampaignAnalyticsAsync(_userIdentityService.BusinessId, filter, page, pageSize);
                return Ok(new ApiResponse<List<ViewCampaignAnalyticsDto>>
                {
                    Message = "Get campaign analytics",
                    Success = true,
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
        [HttpGet("campaign-analytics/{campaignId}")]
        [Authorize]
        public async Task<IActionResult> GetCampaignAnalyticsById(Guid campaignId)
        {
            try
            {
                var result = await _campaignService.GetCampaignAnalyticsDetailsAsync(campaignId);
                return Ok(new ApiResponse<CampaignAnalytsicDto>
                {
                    Message = "Get campaign analytics",
                    Success = true,
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }

        }
        /// <summary>
        /// Handles the request to retrieve schedule campaign based on the provided filter criteria.
        /// </summary>
        /// <param name="filter">The filter criteria to apply when retrieving schedule campaign.</param>
        /// <returns>
        /// Returns an IActionResult:
        /// - 200 OK with a list of campaign message counts if the operation is successful.
        /// - 400 BadRequest with error details if an exception occurs.
        /// </returns>
        [HttpPost("schedule-campaign")]
        [Authorize]
        public async Task<IActionResult> GetScheduleCampaign(FilterDto? filter, [FromQuery] int? page, [FromQuery] int? pageSize)
        {
            try
            {
                var result = await _campaignService.GetScheduleCampaignAsync(_userIdentityService.BusinessId, _userIdentityService.UserId, filter, page, pageSize);
                return Ok(new ApiResponse<List<Campaign>>
                {
                    Message = "Get campaign analytics",
                    Success = true,
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
        [HttpGet("campaign/{id}")]
        [Authorize]
        public async Task<IActionResult> GetCampaignById(Guid id)
        {
            try
            {
                var result = await _campaignService.GetCampaignAsync(_userIdentityService.BusinessId, id);
                return Ok(new ApiResponse<Campaign>
                {
                    Message = "Get campaign",
                    Success = true,
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });

            }
        }
        [HttpGet("campaign-count/{state}")]
        [Authorize]
        public async Task<IActionResult> GetCampaignCount(CampaignState state, [FromQuery] FilterDataDto? filter)
        {
            try
            {
                var result = await _campaignService.GetCampaignCountAsync(_userIdentityService.BusinessId, state, filter?.Condition, filter?.Search);
                return Ok(new ApiResponse<int>
                {
                    Message = "Get campaign analytics",
                    Success = true,
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpGet("ExportCampaignById")]
        [Authorize]
        public async Task<IActionResult> ExportCampaignData([FromQuery] string businessId, [FromQuery] Guid userId, [FromQuery] Guid campaignId)
        {
            try
            {
                if (campaignId.ToString() == null) throw new Exception("Not found campaign id.");

                var dataTable = await _campaignService.GetCampaignExportAsync(businessId, userId, campaignId);
                if (dataTable.Rows.Count == 0)
                {
                    throw new Exception("There is no data associated with this campaign id.");
                }
                using (var workbook = new ClosedXML.Excel.XLWorkbook())
                {
                    var worksheet = workbook.Worksheets.Add("Campaign Data");
                    worksheet.Cell(1, 1).InsertTable(dataTable);

                    var stream = new MemoryStream();
                    workbook.SaveAs(stream);
                    stream.Position = 0;
                    return Ok(new ApiResponse<byte[]>()
                    {
                        Message = "Campaign excel exports",
                        Data = stream.ToArray(),
                        Success = true
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "An error occurred while exporting data.", Error = ex.Message });
            }
        }

        [HttpGet("export-campaign/{id}")]
        [Authorize]
        public async Task<IActionResult> ExportCampaignById(Guid id, CancellationToken cancellationToken)
        {
            try
            {
                return await _campaignService.ExportCampaignByIdAsync(id, CancellationToken.None);
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpPost("CampaignUploadExcelFile")]
        [Authorize]
        public async Task<IActionResult> ExportCampaignById([FromForm] UploadFileDto fileDto)
        {
            try
            {
                var result = await _campaignService.CampaignExcelUploadedFile(fileDto);

                if (result?.UploadResult != null)
                {
                    return Ok(new ApiResponse<CampaignUploadResultDto>
                    {
                        Success = true,
                        Message = $"File uploaded successfully. Contact count: {result.ContactCount}",
                        Data = result
                    });
                }
                else
                {

                    return Ok(new ApiResponse<object>
                    {
                        Success = false,
                        Message = $"File upload failed due to insufficient wallet balance or contact limit exceeded. Contact count: {result?.ContactCount ?? 0}",
                        Data = new
                        {
                            Success = false,
                            ContactCount = result?.ContactCount ?? 0
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        /// <summary>
        /// Get campaign contact statistics including attempted, delivered, read, failed, and reply counts
        /// </summary>
        /// <param name="campaignId">Campaign ID</param>
        /// <returns>Campaign contact statistics</returns>
        [HttpGet("campaign-stats/{campaignId}")]
        [Authorize]
        public async Task<IActionResult> GetCampaignContactStats(Guid campaignId)
        {
            try
            {
                var result = await _campaignService.GetCampaignContactStatsAsync(campaignId);
                return Ok(new ApiResponse<CampaignContactStatsDto>
                {
                    Message = "Campaign contact statistics retrieved successfully.",
                    Success = true,
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        /// <summary>
        /// Get campaign contacts filtered by status with pagination
        /// </summary>
        /// <param name="campaignId">Campaign ID</param>
        /// <param name="status">Conversation status (sent, delivered, read, failed)</param>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10, max: 100)</param>
        /// <returns>Paginated list of campaign contacts with the specified status</returns>
        [HttpGet("campaign-contacts/{campaignId}")]
        [Authorize]
        public async Task<IActionResult> GetCampaignContactsByStatus(
            Guid campaignId,
            [FromQuery] ConvStatus status,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var filter = new CampaignContactFilterDto
                {
                    CampaignId = campaignId,
                    Status = status,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    BusinessId = _userIdentityService.BusinessId
                };

                var result = await _campaignService.GetCampaignContactsByStatusAsync(filter);
                return Ok(new ApiResponse<CampaignContactsResponseDto>
                {
                    Message = "Campaign contacts retrieved successfully.",
                    Success = true,
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        /// <summary>
        /// Get campaign contacts filtered by status with pagination using stored procedure (better performance for large datasets)
        /// </summary>
        /// <param name="campaignId">Campaign ID</param>
        /// <param name="status">Conversation status (sent, delivered, read, failed)</param>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10, max: 100)</param>
        /// <returns>Paginated list of campaign contacts with the specified status</returns>
        [HttpGet("campaign-contacts-sp/{campaignId}")]
        [Authorize]
        public async Task<IActionResult> GetCampaignContactsByStatusStoredProc(
            Guid campaignId,
            [FromQuery] ConvStatus status,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var filter = new CampaignContactFilterDto
                {
                    CampaignId = campaignId,
                    Status = status,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    BusinessId = _userIdentityService.BusinessId
                };

                var result = await _campaignService.GetCampaignContactsByStatusStoredProcAsync(filter);
                return Ok(new ApiResponse<CampaignContactsResponseDto>
                {
                    Message = "Campaign contacts retrieved successfully using stored procedure.",
                    Success = true,
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }


    }
}
