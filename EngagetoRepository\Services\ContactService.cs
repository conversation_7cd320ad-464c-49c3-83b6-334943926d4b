﻿using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.ContactContracts;
using EngagetoContracts.Services;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Dtos.UserDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.Text.Json;
using System.Text;
using Microsoft.AspNetCore.Http;
using EngagetoContracts.GeneralContracts;
using Microsoft.Extensions.Configuration;
namespace EngagetoRepository.Services
{
    public class ContactService : IContactService
    {
        private readonly IJobService _jobService;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ApplicationDbContext _dbContext;
        private readonly IContactRepositoryBase _contactRepository;
        private readonly IUserIdentityService _userIdentityService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IEnvironmentService _environmentService;
        private readonly IConfiguration _configuration;
        private readonly IContactScheduler _contactScheduler;

        public ContactService(IJobService jobService, IBlobStorageService blobStorageService, ApplicationDbContext dbContext, IContactRepositoryBase contactRepositoryBase , IUserIdentityService userIdentityService, IHttpClientFactory httpClientFactory, IEnvironmentService environmentService, IConfiguration configuration, IContactScheduler contactScheduler)
        {
            _jobService = jobService;
            _blobStorageService = blobStorageService;
            _dbContext = dbContext;
            _contactRepository = contactRepositoryBase;
            _userIdentityService = userIdentityService;
            _httpClientFactory = httpClientFactory;
            _environmentService = environmentService;
            _configuration = configuration;
            _contactScheduler = contactScheduler;
        }

        public async Task<FileColumnDto> UploadExcelFileAsync(UploadFileDto uploadedFile)
        {
            try
            {
                if (uploadedFile.File == null) { throw new ArgumentNullException(nameof(uploadedFile.File)); }
                string fileExtension = string.Empty;
                if (!ExcelProcessorcs.IsValidFile(uploadedFile.File, out fileExtension))
                {
                    throw new InvalidOperationException("File format is invalid");
                }
                var key = await _blobStorageService.UploadAsync(uploadedFile);

                List<string> columns = new List<string>();
                Dictionary<string, List<string>> multiSheetColumns = new();

                if (key.UploadedFileName.Split('.').LastOrDefault() == "csv")
                {
                    columns = await ExcelProcessorcs.GetCSVColumns(uploadedFile.File);
                    multiSheetColumns.Add("Default", columns);
                }
                else
                {
                    columns = ExcelProcessorcs.GetFileColumns(uploadedFile.File);
                    multiSheetColumns = ExcelProcessorcs.GetFileColumnsOfMultiSheets(uploadedFile.File);
                }
                FileColumnDto excelColumnsViewModel = new()
                {
                    S3BucketKey = key.UploadedFileName,
                    ColumnNames = columns,
                    MultiSheetColumnNames = multiSheetColumns,
                    UploadedFileId = key.Id,
                    FileName = key.Name
                };
                return excelColumnsViewModel;
            }
            catch (Exception ex)
            {
                throw;
            }

        }
        public async Task<ContactImportTracker> CreateBulkUploadAsync(BulkContactUploadDto bulkContactUploadDto)
        {
            ContactImportTracker customerUploadTracker = bulkContactUploadDto.Adapt<ContactImportTracker>();
            customerUploadTracker.status = UploadStatus.Initiated;
            customerUploadTracker.SheetName = !string.IsNullOrWhiteSpace(bulkContactUploadDto.FileName) ? bulkContactUploadDto.FileName + "/" + bulkContactUploadDto.SheetName :
            bulkContactUploadDto.S3BucketKey?.Split('/').Last() + "/" + bulkContactUploadDto.SheetName;
            customerUploadTracker.FileName = bulkContactUploadDto.FileName;
            customerUploadTracker.UploadedFilesId = bulkContactUploadDto.UploadedFileId;
            customerUploadTracker.BusinessId = Guid.Parse(_userIdentityService.BusinessId);
            customerUploadTracker.UserId = _userIdentityService.UserId;
            customerUploadTracker.CreatedAt = DateTime.UtcNow;
            customerUploadTracker.CreatedBy = _userIdentityService.UserId;
            await _contactRepository.AddAsync(customerUploadTracker);

            var savedTracker = await _contactRepository.GetByIdAysnc(customerUploadTracker.Id);
            if (savedTracker == null)
            {
                throw new Exception("Failed to save the bulk upload tracker");
            }

            var contactBulkUploadData = new ContactBulkUploadData
            { 
                BusinessId = customerUploadTracker.BusinessId.ToString(),
                UserId = customerUploadTracker.UserId
            };

            var input = new
            {
                id = customerUploadTracker.Id,
                JsonData = JsonConvert.SerializeObject(contactBulkUploadData),
                type = "Contacts", 
                scheduledTime = DateTime.UtcNow,
                isDevelopment = _environmentService.IsDevelopment
            };
            var functionUrl = _environmentService.IsDevelopment
                ? _configuration["FunctionSettings:Dev_ScheduleJobUrl"]
                : _configuration["FunctionSettings:Prod_ScheduleJobUrl"];

            if (string.IsNullOrWhiteSpace(functionUrl))
            {
                throw new InvalidOperationException("Function URL is not configured for contact bulk upload processing.");
            }
            var response = await CallFunctionAsync(input, functionUrl ?? string.Empty);
            using var jsonDoc = JsonDocument.Parse(response);

            //await  _contactScheduler.CustomerBulkUploadHandler(customerUploadTracker.Id, contactBulkUploadData.BusinessId, contactBulkUploadData.UserId, "Contacts");


            return customerUploadTracker;
        }

        public async Task<List<Tags>> GetTagDetailsByNameAsync(Guid businessId, List<string> tagsName)
        {
            var tags = await _dbContext.Tags
                .Where(i => i.BusinessId == businessId && tagsName.Contains(i.Tag))
                .ToListAsync();

            return tags;
        }


        private async Task<string> CallFunctionAsync<T>(T requestBody, string functionUrl)
        {
            if (string.IsNullOrEmpty(functionUrl))
            {
                throw new InvalidOperationException("Function URL is not configured.");
            }
            var client = _httpClientFactory.CreateClient();
            var jsonPayload = System.Text.Json.JsonSerializer.Serialize(requestBody);
            var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
            var response = await client.PostAsync(functionUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            Console.WriteLine(result);
            return result;
        }


        public async Task<PaginatedImportTrackerDto> GetContactImportTrackersAsync(int pageNumber, int pageSize)
        {
            try
            {
                  string businessId  =  _userIdentityService.BusinessId;
                var totalCount = _dbContext.ContactImportTrackers.Where(i=> i.BusinessId.ToString()==businessId).AsQueryable().Count();
                var paginatedData = await _dbContext.ContactImportTrackers.Where(i => i.BusinessId.ToString() == businessId)
                    .OrderByDescending(tracker => tracker.CreatedAt)
                    .Join(
                        _dbContext.Users,
                        tracker => tracker.CreatedBy,
                        user => user.Id,
                        (tracker, user) => new
                        {
                            Tracker = tracker, 
                            User = user.Adapt<UserInfoDto>()
                        }
                    )
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();
               
                var contactImportTrackerDtos = paginatedData
                    .Select(x =>
                    {
                        var trackerDto = x.Tracker.Adapt<ContactImportTrackerDto>();
                        trackerDto.CreateUser = x.User;
                        trackerDto.UpdateUser = x.User; 
                        return trackerDto;
                    })
                    .ToList();

                var result = new PaginatedImportTrackerDto
                {
                    TotalCount = totalCount,
                    Trackers = contactImportTrackerDtos
                };

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetContactImportTrackersAsync: {ex}");
                throw new InvalidOperationException($"Something went wrong: {ex.Message}");
            }
        }
        public async Task<ContactImportTracker> GetContactTrackerByIdAsync(int uploadedId)
        {
            var contactImportTracker = await _dbContext.ContactImportTrackers.FirstOrDefaultAsync(i => i.UploadedFilesId == uploadedId);
            return contactImportTracker ?? new();
        }
    }
}
