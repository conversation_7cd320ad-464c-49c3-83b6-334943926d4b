﻿using Dapper;
using EngagetoDapper.Data.Connections;
using EngagetoDapper.Data.Dtos;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using Microsoft.Extensions.Logging;
using SqlKata;
using SqlKata.Compilers;
using SqlKata.Execution;
using System.Collections;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace EngagetoDapper.Data.Dapper.Repositories.GenericRepositories
{
    public class GenericRepository : IGenericRepository
    {
        private readonly ISqlConnectionFactory _connectionFactory;
        private readonly ILogger<GenericRepository> _logger;
        private bool _disposed = false;

        public GenericRepository(ISqlConnectionFactory connectionFactory, ILogger<GenericRepository> logger)
        {
            _connectionFactory = connectionFactory;
            _logger = logger;
        }

        public async Task<List<T>> GetAllAsync<T>()
        {
            try
            {
                using var connection = _connectionFactory.CreateConnection();
                var db = new QueryFactory(connection, new SqlServerCompiler());
                var query = new Query(GetTableName<T>());
                var result = await db.GetAsync<T>(query);
                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetAllAsync<{Type}>", typeof(T).Name);
                return new();
            }
        }

        public async Task<List<T>> GetByGuidIdAsync<T>(Dictionary<Guid, string> filterValues)
        {
            try
            {
                using var connection = _connectionFactory.CreateConnection();
                var db = new QueryFactory(connection, new SqlServerCompiler());
                var query = new Query(GetTableName<T>());

                foreach (var filter in filterValues)
                {
                    query.Where(filter.Value, filter.Key);
                }

                var result = await db.GetAsync<T>(query);
                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetByGuidIdAsync<{Type}>", typeof(T).Name);
                return new();
            }
        }

        public async Task<List<T>> GetByObjectAsync<T>(Dictionary<string, object> filterValues, string? tableName = null)
        {
            try
            {
                using var connection = _connectionFactory.CreateConnection();
                tableName ??= GetTableName<T>();
                var db = new QueryFactory(connection, new SqlServerCompiler());
                var query = new Query($"{tableName} WITH (NOLOCK)");

                foreach (var filter in filterValues)
                {
                    query.Where(filter.Key, filter.Value);
                }

                var result = await db.GetAsync<T>(query);
                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetByObjectAsync<{Type}>", typeof(T).Name);
                return new();
            }
        }

        public async Task<bool> IsExistAsync<T>(Dictionary<string, object> filterValues, string? tableName = null)
        {
            try
            {
                using var connection = _connectionFactory.CreateConnection();
                tableName ??= GetTableName<T>();
                var db = new QueryFactory(connection, new SqlServerCompiler());
                var query = new Query(tableName);

                foreach (var filter in filterValues)
                {
                    query.Where(filter.Key, filter.Value);
                }

                query.Limit(1);
                return await db.ExistsAsync(query);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in IsExistAsync<{Type}>", typeof(T).Name);
                return false;
            }
        }

        public async Task<int> SaveAsync<T>(T entity)
        {
            try
            {
                using var connection = _connectionFactory.CreateConnection();
                var db = new QueryFactory(connection, new SqlServerCompiler());
                var query = new Query(GetTableName<T>()).AsInsert(entity);
                return await db.ExecuteAsync(query);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SaveAsync<{Type}>", typeof(T).Name);
                return 0;
            }
        }
        public async Task<List<T>> GetByNameAsync<T>(Dictionary<string, string> filterValues)
        {
            try
            {
                using var connection = _connectionFactory.CreateConnection();
                var db = new QueryFactory(connection, new SqlServerCompiler());
                var query = new Query(GetTableName<T>());

                foreach (var filter in filterValues)
                {
                    query.WhereContains(filter.Value, filter.Key);
                }

                var result = await db.GetAsync<T>(query);
                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetByNameAsync<{Type}>", typeof(T).Name);
                return new();
            }
        }

        public async Task<bool> UpdateRecordAsync<T>(string tableName, List<string> columns, T entity, Dictionary<string, object> conditions)
        {
            try
            {
                using var connection = _connectionFactory.CreateConnection();
                var setClauses = columns.Select(col => $"[{col}] = @{col}");
                var setClauseString = string.Join(", ", setClauses);

                var whereClauses = conditions.Select(condition => $"{condition.Key} = @{condition.Key}");
                var whereClauseString = "WHERE " + string.Join(" AND ", whereClauses);

                var sqlQuery = $@"
                UPDATE {tableName}
                SET {setClauseString}
                {whereClauseString}";

                var parameters = new DynamicParameters(entity);
                foreach (var condition in conditions)
                {
                    parameters.Add(condition.Key, condition.Value);
                }

                var result = await connection.ExecuteAsync(sqlQuery, parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in UpdateRecordAsync (bulk) for {TableName}", tableName);
                return false;
            }
        }
        public async Task<T?> UpdateRecordAsync<T>(T entity, Dictionary<string, object>? filterValues = null)
        {
            try
            {
                using var connection = _connectionFactory.CreateConnection();
                var db = new QueryFactory(connection, new SqlServerCompiler());
                var tableName = GetTableName<T>();
                var updateQuery = new Query(tableName);

                if (filterValues != null)
                    foreach (var filter in filterValues)
                        updateQuery.Where(filter.Key, filter.Value);

                updateQuery.AsUpdate(entity);
                var updateResult = await db.ExecuteAsync(updateQuery);

                if (updateResult > 0)
                {
                    var selectQuery = new Query(tableName);
                    if (filterValues != null)
                        foreach (var filter in filterValues)
                            selectQuery.Where(filter.Key, filter.Value);

                    return await db.FirstOrDefaultAsync<T>(selectQuery);
                }

                return default;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in UpdateRecordAsync<{Type}>", typeof(T).Name);
                return default;
            }
        }


        public async Task<bool> InsertRecordsAsync<T>(string tableName, List<string> columns, List<T> entities)
        {
            try
            {
                using var connection = _connectionFactory.CreateConnection();
                var columnNames = string.Join(", ", columns.Select(c => $"[{c}]"));
                var parameterNames = string.Join(", ", columns.Select(c => $"@{c}"));
                var query = $"INSERT INTO {tableName} ({columnNames}) VALUES ({parameterNames})";

                var result = await connection.ExecuteAsync(query, entities);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in InsertRecordsAsync<{Type}>", typeof(T).Name);
                return false;
            }
        }

        public async Task<bool> FindAndUpdateAsync(string tableName, Dictionary<string, object> updatedValues, Dictionary<string, object> conditions)
        {
            try
            {
                using var connection = _connectionFactory.CreateConnection();
                var setClause = string.Join(", ", updatedValues.Keys.Select(k => $"[{k}] = @{k}"));
                var whereClause = string.Join(" AND ", conditions.Keys.Select(k => $"[{k}] = @{k}"));
                var sql = $"UPDATE {tableName} SET {setClause} WHERE {whereClause}";

                var parameters = new DynamicParameters();
                foreach (var kvp in updatedValues) parameters.Add(kvp.Key, kvp.Value);
                foreach (var kvp in conditions) parameters.Add(kvp.Key, kvp.Value);

                var result = await connection.ExecuteAsync(sql, parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in FindAndUpdateAsync for {TableName}", tableName);
                return false;
            }
        }

        public async Task<bool> UpdateRecordsByIdsAsync<TId, TEntity>(string tableName, IEnumerable<string> columnsToUpdate, TEntity updateValues, string idColumn, IEnumerable<TId> ids)
        {
            try
            {
                using var connection = _connectionFactory.CreateConnection();

                var setClause = string.Join(", ", columnsToUpdate.Select(col => $"[{col}] = @{col}"));
                var idParams = ids.Select((id, i) => $"@id{i}").ToList();
                var whereClause = $"WHERE [{idColumn}] IN ({string.Join(", ", idParams)})";
                var sql = $"UPDATE [{tableName}] SET {setClause} {whereClause}";

                var parameters = new DynamicParameters(updateValues);
                int index = 0;
                foreach (var id in ids)
                {
                    parameters.Add($"id{index++}", id);
                }

                var result = await connection.ExecuteAsync(sql, parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in UpdateRecordsByIdsAsync for {TableName}", tableName);
                return false;
            }
        }
        public async Task<bool> DeleteRecordAsync<T>(string tableName, List<RequestFilterDto> requestFilters)
        {
            try
            {
                using var connection = _connectionFactory.CreateConnection();
                var whereClauses = requestFilters.Select(condition => $"{condition.Key} {condition.Operator} @{condition.Key}").ToList();
                var whereClauseString = "WHERE " + string.Join(" AND ", whereClauses);
                string sqlQuery = $"DELETE FROM {tableName} {whereClauseString}";

                var parameters = new DynamicParameters();
                foreach (var condition in requestFilters)
                {
                    parameters.Add(condition.Key, condition.Value);
                }

                var result = await connection.ExecuteAsync(sqlQuery, parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in DeleteRecordAsync for {TableName}", tableName);
                return false;
            }
        }

        public async Task<List<T>> GetRecordByRequestFilter<T>(List<RequestFilterDto> requestFilters, string? tableName = null, int? page = 0, int? pageSize = 0, List<string>? columns = null)
        {
            try
            {
                tableName ??= GetTableName<T>();
                var whereClauses = new List<string>();
                var orGroups = new List<List<string>>();
                List<string>? currentGroup = null;
                string order = string.Empty;
                string key = string.Empty;

                foreach (var filter in requestFilters)
                {
                    var columnName = filter.Key;
                    var value = filter.Value;
                    var logicalOperator = filter.LogicalOperator?.ToLowerInvariant() ?? "and";

                    if (logicalOperator == "or")
                    {
                        if (currentGroup != null)
                        {
                            orGroups.Add(currentGroup);
                        }
                        currentGroup = new List<string>();
                    }

                    if (new[] { "desc", "asc" }.Contains(filter.Operator.ToLower()))
                    {
                        order = filter.Operator.ToUpperInvariant();
                        key = filter.Key;
                    }
                    else
                    {
                        string? condition = BuildCondition(columnName, filter.Operator, value);
                        if (!string.IsNullOrEmpty(condition))
                        {
                            if (currentGroup != null)
                                currentGroup.Add(condition);
                            else
                                whereClauses.Add(condition);
                        }
                    }
                }

                if (currentGroup != null)
                {
                    orGroups.Add(currentGroup);
                }

                var finalWhereClause = whereClauses.Any() ? string.Join(" AND ", whereClauses) : "1=1";

                if (orGroups.Any())
                {
                    var orConditions = orGroups.Select(group => "(" + string.Join(" OR ", group) + ")").ToList();
                    finalWhereClause += " AND (" + string.Join(" OR ", orConditions) + ")";
                }

                string paginationClause = string.Empty;
                if (page > 0 && pageSize > 0)
                {
                    int skip = (page - 1) * pageSize ?? 0;
                    paginationClause = $"OFFSET {skip} ROWS FETCH NEXT {pageSize} ROWS ONLY";
                }

                string selectClause = columns != null && columns.Any() ? string.Join(", ", columns) : "*";
                string sqlQuery = $@"SELECT {selectClause} FROM {tableName} WITH (NOLOCK) WHERE {finalWhereClause}";

                if (!string.IsNullOrEmpty(order))
                {
                    sqlQuery += $" ORDER BY {key} {order}";
                }

                sqlQuery += $" {paginationClause}";
                using var connection = _connectionFactory.CreateConnection();
                var result = await connection.QueryAsync<T>(sqlQuery);
                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetRecordByRequestFilter<{Type}>", typeof(T).Name);
                return new();
            }
        }

        private string? BuildCondition(string columnName, string operatorValue, object value)
        {
            switch (operatorValue.ToLower())
            {
                case "=":
                case "eq":
                    return $"{columnName} = '{value}'";
                case "!=":
                case "<>":
                case "ne":
                    return $"{columnName} <> '{value}'";
                case "<":
                case "lt":
                    return $"{columnName} < '{value}'";
                case "<=":
                case "lte":
                    return $"{columnName} <= '{value}'";
                case ">":
                case "gt":
                    return $"{columnName} > '{value}'";
                case ">=":
                case "gte":
                    return $"{columnName} >= '{value}'";
                case "like":
                    return $"{columnName} LIKE '%{value}%'";
                case "startwith":
                case "starts":
                    return $"{columnName} LIKE '{value}%'";
                case "endwith":
                case "ends":
                    return $"{columnName} LIKE '%{value}'";
                case "in":
                    var inList = ((IList)value).Cast<object>().Select(v => $"'{v}'").ToList();
                    return $"{columnName} IN ({string.Join(", ", inList)})";
                case "not in":
                    var notInList = ((IList)value).Cast<object>().Select(v => $"'{v}'").ToList();
                    return $"{columnName} NOT IN ({string.Join(", ", notInList)})";
                default:
                    return null;
            }
        }
        private string GetTableName<T>()
        {
            var tableAttribute = (TableAttribute?)Attribute.GetCustomAttribute(typeof(T), typeof(TableAttribute));
            return tableAttribute?.Name ?? typeof(T).Name;
        }

        public async Task<int> Count<T>(Dictionary<string, object> filters, string? tableName = null)
        {
            // Infer table name from type if not provided
            string actualTableName = tableName ?? GetTableName<T>();

            var sqlBuilder = new StringBuilder($"SELECT COUNT(*) FROM [{actualTableName}]");

            if (filters != null && filters.Count > 0)
            {
                sqlBuilder.Append(" WHERE ");
                var conditions = new List<string>();
                foreach (var key in filters.Keys)
                {
                    conditions.Add($"[{key}] = @{key}");
                }
                sqlBuilder.Append(string.Join(" AND ", conditions));
            }
            using var connection = _connectionFactory.CreateConnection();
            // Use Dapper's parameterization to prevent SQL injection
            var count = await connection.ExecuteScalarAsync<int>(sqlBuilder.ToString(), filters);
            return count;
        }
        public async Task<List<T>> GetColumnValuesAsync<T>(
            List<RequestFilterDto> filters,
            string columnName,
            string tableName,
            string? orderByColumn = null,
            string? orderDirection = "ASC",
            int? page = 0,
            int? pageSize = 0)
        {
            var whereClauses = new List<string>();
            var parameters = new DynamicParameters();

            for (int i = 0; i < filters.Count; i++)
            {
                var filter = filters[i];
                var clause = BuildCondition(filter.Key, filter.Operator, filter.Value);

                if (!string.IsNullOrWhiteSpace(clause))
                {
                    parameters.Add($"@{filter.Key}", filter.Value);

                    if (i == 0)
                        whereClauses.Add(clause);
                    else
                        whereClauses.Add($"{filter.LogicalOperator?.ToUpperInvariant() ?? "AND"} {clause}");
                }
            }

            var whereClause = whereClauses.Any() ? $"WHERE {string.Join(" ", whereClauses)}" : "";

            string orderClause = "";
            if (!string.IsNullOrWhiteSpace(orderByColumn))
            {
                var direction = string.Equals(orderDirection, "DESC", StringComparison.OrdinalIgnoreCase) ? "DESC" : "ASC";
                orderClause = $"ORDER BY {orderByColumn} {direction}";
            }

            string paginationClause = "";
            if (page > 0 && pageSize > 0)
            {
                int skip = ((page ?? 1) - 1) * (pageSize ?? 0);
                paginationClause = $"OFFSET {skip} ROWS FETCH NEXT {pageSize} ROWS ONLY";
            }

            // NOTE: Bracket the table name to prevent SQL injection, and add WITH (NOLOCK)
            string sql = $@"
                SELECT {columnName}
                FROM [{tableName}] WITH (NOLOCK)
                {whereClause}
                {orderClause}
                {paginationClause}";

            using var connection = _connectionFactory.CreateConnection();
            var result = await connection.QueryAsync<T>(sql, parameters);
            return result.ToList();
        }
    }
}
