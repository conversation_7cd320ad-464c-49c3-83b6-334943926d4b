using EngagetoEntities.Dtos.CampaignDto;
using EngagetoEntities.Enums;

namespace EngagetoEntities.Examples
{
    /// <summary>
    /// Example usage of Campaign Contact Statistics API
    /// </summary>
    public static class CampaignContactStatsExample
    {
        /// <summary>
        /// Example API call to get campaign contact statistics
        /// 
        /// API Endpoint: GET /api/Campaigns/campaign-stats/{campaignId}
        /// 
        /// This method fetches comprehensive statistics for a campaign including:
        /// - Total attempted contacts (from CampaignTracker table)
        /// - Delivered count (ConvStatus.delivered)
        /// - Read count (ConvStatus.read) 
        /// - Failed count (ConvStatus.failed)
        /// - Reply count (conversations with ReplyId matching campaign WhatsApp message IDs)
        /// - Sent count (ConvStatus.sent)
        /// 
        /// The method works by:
        /// 1. Getting campaign details from Campaigns table
        /// 2. Counting total attempted from CampaignTracker table
        /// 3. Getting WhatsApp message IDs from campaign
        /// 4. Querying Conversations table for status counts
        /// 5. Counting replies by matching ReplyId with campaign message IDs
        /// </summary>
        public static CampaignContactStatsDto ExampleResponse()
        {
            return new CampaignContactStatsDto
            {
                CampaignId = Guid.Parse("12345678-1234-1234-1234-123456789012"),
                CampaignTitle = "Summer Sale Campaign",
                AttemptedCount = 1000,      // Total contacts attempted
                SentCount = 950,            // ConvStatus.sent
                DeliveredCount = 900,       // ConvStatus.delivered  
                ReadCount = 750,            // ConvStatus.read
                FailedCount = 50,           // ConvStatus.failed
                ReplyCount = 125,           // Replies received (not an enum)
                CreatedDate = DateTime.UtcNow.AddDays(-7),
                CreatedBy = "<EMAIL>"
            };
        }

        /// <summary>
        /// Example HTTP request using HttpClient
        /// </summary>
        public static async Task<CampaignContactStatsDto?> GetCampaignStatsAsync(
            HttpClient httpClient, 
            Guid campaignId, 
            string authToken)
        {
            try
            {
                // Set authorization header
                httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", authToken);

                // Make API call
                var response = await httpClient.GetAsync($"/api/Campaigns/campaign-stats/{campaignId}");
                
                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var apiResponse = System.Text.Json.JsonSerializer.Deserialize<ApiResponse<CampaignContactStatsDto>>(jsonContent);
                    return apiResponse?.Data;
                }
                
                return null;
            }
            catch (Exception ex)
            {
                // Handle exception
                Console.WriteLine($"Error getting campaign stats: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Example of how the statistics are calculated
        /// </summary>
        public static void ExplainCalculations()
        {
            /*
             * ATTEMPTED COUNT:
             * - Counted from CampaignTracker table where CampaignId matches
             * - Represents total contacts that were targeted for the campaign
             * 
             * STATUS COUNTS (from Conversations table):
             * - SENT: ConvStatus.sent - Messages successfully sent to WhatsApp
             * - DELIVERED: ConvStatus.delivered - Messages delivered to recipient's device
             * - READ: ConvStatus.read - Messages read by recipient
             * - FAILED: ConvStatus.failed - Messages that failed to send
             * 
             * REPLY COUNT:
             * - NOT an enum value, calculated separately
             * - Counts conversations where ReplyId matches any WhatsApp message ID from the campaign
             * - Represents actual customer responses/engagement
             * 
             * WORKFLOW:
             * 1. Campaign created with target contacts
             * 2. CampaignTracker records created (AttemptedCount)
             * 3. Messages sent via WhatsApp API (SentCount)
             * 4. WhatsApp webhooks update status (DeliveredCount, ReadCount, FailedCount)
             * 5. Customer replies tracked via ReplyId (ReplyCount)
             */
        }
    }

    /// <summary>
    /// Generic API response wrapper (if not already defined)
    /// </summary>
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public string? Errors { get; set; }
    }
}
