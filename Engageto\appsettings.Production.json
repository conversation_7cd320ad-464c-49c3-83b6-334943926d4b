{
  "ConnectionStrings": {
    // dev database
    "ConnStr": "Data Source=engageto.database.windows.net;Initial Catalog=qa-engageto;User Id=dbmasteruser;Password=************`H9hTJK5ojM)C=$C69,2;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
    // production database
   // "ConnStr": "Data Source=engageto-prd.database.windows.net;Initial Catalog=engageto-prd;User Id=dbmasteruser;Password=************`H9hTJK5ojM)C=$C69,1;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30; Max Pool Size=500;Min Pool Size=5;"

  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}