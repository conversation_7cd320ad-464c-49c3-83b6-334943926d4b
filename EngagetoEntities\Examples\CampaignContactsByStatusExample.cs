using EngagetoEntities.Dtos.CampaignDto;
using EngagetoEntities.Enums;

namespace EngagetoEntities.Examples
{
    /// <summary>
    /// Example usage of Campaign Contacts by Status API
    /// </summary>
    public static class CampaignContactsByStatusExample
    {
        /// <summary>
        /// Example API calls to get campaign contacts filtered by status
        /// 
        /// DAPPER APPROACH (Flexible, LINQ-like):
        /// API Endpoint: GET /api/Campaigns/campaign-contacts/{campaignId}?status={status}&pageNumber={page}&pageSize={size}
        /// 
        /// STORED PROCEDURE APPROACH (Better performance for large datasets):
        /// API Endpoint: GET /api/Campaigns/campaign-contacts-sp/{campaignId}?status={status}&pageNumber={page}&pageSize={size}
        /// 
        /// This method:
        /// 1. Queries Conversations table using ReferenceId (mapped to CampaignId)
        /// 2. Filters conversations based on ConvStatus enum (sent=0, delivered=1, read=2, failed=3)
        /// 3. Extracts ContactNumbers from Conversations.To field
        /// 4. Joins with Contacts table to fetch full contact details
        /// 5. Applies pagination (pageNumber and pageSize)
        /// 6. Returns paginated results with total count and status summary
        /// </summary>
        public static CampaignContactsResponseDto ExampleResponse()
        {
            return new CampaignContactsResponseDto
            {
                Contacts = new List<CampaignContactDetailDto>
                {
                    new CampaignContactDetailDto
                    {
                        ContactId = Guid.Parse("11111111-1111-1111-1111-111111111111"),
                        Name = "John Doe",
                        Contact = "1234567890",
                        CountryCode = "+1",
                        Email = "<EMAIL>",
                        Tags = "VIP,Customer",
                        ChatStatus = ChatStatus.Open,
                        CreatedDate = DateTime.UtcNow.AddDays(-30),
                        IsActive = true,
                        ConversationStatus = ConvStatus.delivered,
                        ConversationCreatedAt = DateTime.UtcNow.AddHours(-2),
                        WhatsAppMessageId = "wamid.12345",
                        TextMessage = "Hello! Check out our summer sale!",
                        ErrorMessage = null
                    },
                    new CampaignContactDetailDto
                    {
                        ContactId = Guid.Parse("*************-2222-2222-************"),
                        Name = "Jane Smith",
                        Contact = "9876543210",
                        CountryCode = "+1",
                        Email = "<EMAIL>",
                        Tags = "Lead",
                        ChatStatus = ChatStatus.New,
                        CreatedDate = DateTime.UtcNow.AddDays(-15),
                        IsActive = true,
                        ConversationStatus = ConvStatus.delivered,
                        ConversationCreatedAt = DateTime.UtcNow.AddHours(-1),
                        WhatsAppMessageId = "wamid.67890",
                        TextMessage = "Hello! Check out our summer sale!",
                        ErrorMessage = null
                    }
                },
                TotalCount = 150,
                PageNumber = 1,
                PageSize = 10,
                StatusSummary = new CampaignStatusSummaryDto
                {
                    SentCount = 200,
                    DeliveredCount = 150,
                    ReadCount = 100,
                    FailedCount = 10
                }
            };
        }

        /// <summary>
        /// Example HTTP requests using HttpClient
        /// </summary>
        public static class HttpExamples
        {
            /// <summary>
            /// Get delivered messages for a campaign (Dapper approach)
            /// </summary>
            public static async Task<CampaignContactsResponseDto?> GetDeliveredContactsDapperAsync(
                HttpClient httpClient, 
                Guid campaignId, 
                string authToken,
                int pageNumber = 1,
                int pageSize = 10)
            {
                try
                {
                    httpClient.DefaultRequestHeaders.Authorization = 
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", authToken);

                    var url = $"/api/Campaigns/campaign-contacts/{campaignId}?status={(int)ConvStatus.delivered}&pageNumber={pageNumber}&pageSize={pageSize}";
                    var response = await httpClient.GetAsync(url);
                    
                    if (response.IsSuccessStatusCode)
                    {
                        var jsonContent = await response.Content.ReadAsStringAsync();
                        var apiResponse = System.Text.Json.JsonSerializer.Deserialize<ApiResponse<CampaignContactsResponseDto>>(jsonContent);
                        return apiResponse?.Data;
                    }
                    
                    return null;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error getting delivered contacts: {ex.Message}");
                    return null;
                }
            }

            /// <summary>
            /// Get failed messages for a campaign (Stored Procedure approach - better performance)
            /// </summary>
            public static async Task<CampaignContactsResponseDto?> GetFailedContactsStoredProcAsync(
                HttpClient httpClient, 
                Guid campaignId, 
                string authToken,
                int pageNumber = 1,
                int pageSize = 50)
            {
                try
                {
                    httpClient.DefaultRequestHeaders.Authorization = 
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", authToken);

                    var url = $"/api/Campaigns/campaign-contacts-sp/{campaignId}?status={(int)ConvStatus.failed}&pageNumber={pageNumber}&pageSize={pageSize}";
                    var response = await httpClient.GetAsync(url);
                    
                    if (response.IsSuccessStatusCode)
                    {
                        var jsonContent = await response.Content.ReadAsStringAsync();
                        var apiResponse = System.Text.Json.JsonSerializer.Deserialize<ApiResponse<CampaignContactsResponseDto>>(jsonContent);
                        return apiResponse?.Data;
                    }
                    
                    return null;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error getting failed contacts: {ex.Message}");
                    return null;
                }
            }
        }

        /// <summary>
        /// ConvStatus enum values for API calls
        /// </summary>
        public static class StatusValues
        {
            public const int Sent = 0;      // ConvStatus.sent
            public const int Delivered = 1; // ConvStatus.delivered  
            public const int Read = 2;      // ConvStatus.read
            public const int Failed = 3;   // ConvStatus.failed
            public const int Received = 4; // ConvStatus.received
            public const int Sending = 5;  // ConvStatus.sending
        }

        /// <summary>
        /// Example filter DTO for programmatic usage
        /// </summary>
        public static CampaignContactFilterDto CreateFilterExample()
        {
            return new CampaignContactFilterDto
            {
                CampaignId = Guid.Parse("12345678-1234-1234-1234-123456789012"),
                Status = ConvStatus.delivered,
                PageNumber = 1,
                PageSize = 20,
                BusinessId = "company-123"
            };
        }

        /// <summary>
        /// Explanation of the data flow and relationships
        /// </summary>
        public static void ExplainDataFlow()
        {
            /*
             * DATA FLOW EXPLANATION:
             * 
             * 1. CAMPAIGN CREATION:
             *    - Campaign created with CampaignId
             *    - Messages sent to contacts via WhatsApp API
             * 
             * 2. CONVERSATION TRACKING:
             *    - Each message creates a Conversations record
             *    - ReferenceId = CampaignId.ToString() (links conversation to campaign)
             *    - To = Contact phone number (for joining with Contacts table)
             *    - Status = ConvStatus enum (sent, delivered, read, failed)
             * 
             * 3. CONTACT JOINING:
             *    - Join Conversations.To with Contacts.CountryCode + Contacts.Contact
             *    - Phone number normalization: REPLACE(phone, '+', '') for consistent matching
             * 
             * 4. FILTERING & PAGINATION:
             *    - Filter by ReferenceId (CampaignId) and Status (enum value)
             *    - Apply business security filter (BusinessId)
             *    - Use ROW_NUMBER() for efficient pagination
             * 
             * 5. RESPONSE STRUCTURE:
             *    - Paginated contact list with conversation details
             *    - Total count for pagination UI
             *    - Status summary for analytics dashboard
             * 
             * PERFORMANCE CONSIDERATIONS:
             * - Use Dapper for flexibility and readable SQL
             * - Use Stored Procedures for complex joins with large datasets
             * - Add indexes on ReferenceId, Status, and phone number fields
             * - Limit page size to prevent memory issues (max 100)
             */
        }
    }

    /// <summary>
    /// Generic API response wrapper (if not already defined)
    /// </summary>
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public string? Errors { get; set; }
    }
}
