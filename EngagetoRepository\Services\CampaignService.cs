﻿using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using EngagetoBackGroundJobs.Implementation;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.CampaignContracts;
using EngagetoContracts.ContactContracts;
using EngagetoContracts.GeneralContracts;
using EngagetoContracts.Services;
using EngagetoContracts.UserContracts;
using EngagetoDapper.Data.Dapper.Services.FilterServices;
using EngagetoDapper.Data.Dtos;
using EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignRepositories;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoDapper.Data.Connections;
using EngagetoEntities;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.CampaignDto;
using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Dtos.FilterDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.Utilities;
using Humanizer;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Dapper;
using OfficeOpenXml;
using System.Data;
using System.Linq.Expressions;
using System.Text;
using System.Text.Json;

namespace EngagetoRepository.Services
{
    public class CampaignService : ICampaignService
    {
        private readonly EngagetoDapper.Data.Dapper.Repositories.InboxRepositories.IInboxRepository _inboxRepository;
        private readonly IGenericRepository _genericRepository;
        private readonly ICampaignScheduler _campaignScheduler;
        private readonly ICampaignRespository _campaignRespository;
        private readonly IContactRepositoryBase _contactRepository;
        private readonly IFilterService _filterService;
        private readonly ApplicationDbContext _appDbContext;
        private readonly ILogHistoryService _logHistoryService;
        private readonly IBlobStorageService _blobStorageService;
        private readonly IUserIdentityService _userIdentityService;
        private ApplicationDbContext _campaignDbContext;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        private readonly string _schedulerJoburl;
        private readonly string _terminateJobUrl;
        private readonly IEnvironmentService _environmentService;
        private readonly IWalletService _walletService;
        private readonly ISqlConnectionFactory _connectionFactory;


        public CampaignService(IGenericRepository genericRepository,
            EngagetoDapper.Data.Dapper.Repositories.InboxRepositories.IInboxRepository inboxRepository,
            ICampaignScheduler campaignScheduler, ILogHistoryService logHistoryService,
            ApplicationDbContext appDbContext,
            IFilterService filterService,
            ICampaignRespository campaignRespository, IBlobStorageService blobStorageService, IUserIdentityService userIdentityService,
            ApplicationDbContext campaignDbContext,
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration,
            IEnvironmentService environmentService,
            IContactRepositoryBase contactRepositoryBase,
            IWalletService walletService,
            ISqlConnectionFactory connectionFactory
            )
        {
            _genericRepository = genericRepository;
            _inboxRepository = inboxRepository;
            _appDbContext = appDbContext;
            _logHistoryService = logHistoryService;
            _campaignScheduler = campaignScheduler;
            _filterService = filterService;
            _campaignRespository = campaignRespository;
            _blobStorageService = blobStorageService;
            _userIdentityService = userIdentityService;
            _campaignDbContext = campaignDbContext;
            _configuration = configuration;
            _httpClientFactory = httpClientFactory;
            _environmentService = environmentService;
            _contactRepository = contactRepositoryBase;
            _walletService = walletService;
            _connectionFactory = connectionFactory;


            if (_environmentService.IsDevelopment)
            {
                _schedulerJoburl = _configuration["FunctionSettings:Dev_ScheduleJobUrl"] ?? "";
                _terminateJobUrl = _configuration["FunctionSettings:Dev_TerminateJobUrl"] ?? "";
            }
            else
            {
                _schedulerJoburl = _configuration["FunctionSettings:Prod_ScheduleJobUrl"] ?? "";
                _terminateJobUrl = _configuration["FunctionSettings:Prod_TerminateJobUrl"] ?? "";
            }
        }


        public async Task<bool> CreateAsync(Guid userId, string userName, CreateCampaignDto dto)
        {
            string jobId = string.Empty;
            if (string.IsNullOrEmpty(dto.Name))
            {
                throw new InvalidDataException("Campaign name cannot be empty.");
            }
            try
            {
                if (!(await IsExistCampaignNameAsync(dto.BusinessId, dto.Name)) || (dto.Id != null && dto.Id != Guid.Empty && dto?.ScheduleDate != null && dto.ScheduleDate != DateTime.MinValue))
                {
                    var campaign = (await _genericRepository.GetByObjectAsync<Campaign>(new Dictionary<string, object> { { "CampaignId", dto.Id ?? Guid.Empty } }, "Campaigns")).FirstOrDefault();
                    if (campaign != null)
                    {
                        if (!string.IsNullOrEmpty(campaign.ScheduleJobId))
                        {
                            var functionUrl = _terminateJobUrl;
                            var terminateRequestBody = new { JobId = campaign.ScheduleJobId };
                            await CallFunctionAsync(terminateRequestBody, functionUrl ?? string.Empty);

                            await _genericRepository.DeleteRecordAsync<CampaignTracker>(nameof(CampaignTracker), new()
                            {
                                new(nameof(CampaignTracker.CampaignId),campaign.CampaignId,"="),
                                new(nameof(CampaignTracker.BusinessId),campaign.BusinessId,"=")
                            });
                        }

                        if (!string.IsNullOrEmpty(campaign.ChildScheduleJobIds))
                        {
                            var jobIds = campaign.ChildScheduleJobIds
                                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                                .Select(id => id.Trim())
                                .Where(id => !string.IsNullOrEmpty(id));
                            foreach (var job in jobIds)
                            {
                                var functionUrl = _terminateJobUrl;
                                var terminateRequestBody = new { job = job };
                                await CallFunctionAsync(terminateRequestBody, functionUrl ?? string.Empty);
                            }
                        }
                    }
                    else
                    {
                        campaign = new();
                        campaign.CampaignId = Guid.NewGuid();
                        campaign.CampaignTitle = dto.Name;
                        campaign.UserId = _userIdentityService.UserId;
                        campaign.BusinessId = _userIdentityService.BusinessId;
                        campaign.Createdby = _userIdentityService.Name;
                        campaign.CreatedDate = DateTime.UtcNow;
                    }
                    campaign.UploadedFileId = dto.UploadedFileId;
                    var audienceList = dto.Audiences?.Select(audience => audience.ToString()).ToList();
                    campaign.Audiance = audienceList != null && audienceList.Count() > 1000 ? string.Join(",", audienceList) : null;
                    campaign.SendTextType = dto.Text;
                    campaign.MediaUrl = dto.MediaUrl;
                    campaign.TemplateId = dto.Template?.Id;
                    campaign.DateSetLive = dto.ScheduleDate;
                    campaign.HeaderValue = dto.Template?.HeaderValue != null
                        ? ((string.IsNullOrEmpty(dto.Template.HeaderValue.Value) || StringHelper.IsLeadratVariable(dto.Template.HeaderValue.Value))
                        ? dto.Template.HeaderValue.FallbackValue
                        : dto.Template.HeaderValue.Value) : null;
                    campaign.BodyValues = dto.Template?.BodyValues != null ? string.Join(",", dto.Template.BodyValues.Select(i => ((string.IsNullOrEmpty(i.Value) || StringHelper.IsLeadratVariable(i.Value))) ? i.FallbackValue : i.Value)) : null;
                    campaign.EditedDate = DateTime.UtcNow;
                    campaign.Editedby = userName;

                    var carouselVariables = new List<CarouselCardVariableDto>();
                    if (dto.Template != null && dto.Template.CarouselVariables != null)
                    {
                        foreach (var carousel in dto.Template.CarouselVariables)
                        {
                            var bodyVariables = carousel.BodyCarouselVariableValues != null
                                ? carousel.BodyCarouselVariableValues.Select(i => string.IsNullOrEmpty(i.Value) || StringHelper.IsLeadratVariable(i.Value) ? i.FallbackValue : i.Value).ToList()
                                : new();

                            var redirectUrlVariables = carousel.RedirectUrlVariableValues != null
                                ? carousel.RedirectUrlVariableValues.Select(i => string.IsNullOrEmpty(i.Value) || StringHelper.IsLeadratVariable(i.Value) ? i.FallbackValue : i.Value).ToList()
                                : new();

                            var carouselDto = new CarouselCardVariableDto
                            {
                                BodyCarouselVariableValues = bodyVariables.ToArray(),
                                RedirectUrlVariableValues = redirectUrlVariables.ToArray(),
                                MediaUrl = null
                            };
                            if (carouselDto != null)
                            {
                                carouselVariables.Add(carouselDto);
                            }
                        }
                    }
                    campaign.CarouselVariables = JsonConvert.SerializeObject(carouselVariables);

                    var columns = StringHelper.GetPropertyNames<Campaign>(false);
                    var result = false;
                    if (dto?.ScheduleDate != null && dto.ScheduleDate != DateTime.MinValue && campaign.DateSetLive.Value > DateTime.UtcNow)
                    {
                        await SaveCampaignTrackersAsync(campaign.CampaignId, campaign.BusinessId, campaign.UserId, audienceList ?? new());
                        campaign.State = CampaignState.Scheduled;
                        var input = new
                        {
                            id = campaign.CampaignId,
                            JsonData = JsonConvert.SerializeObject(campaign),
                            type = "Campaign",
                            scheduledTime = campaign.DateSetLive,
                            isDevelopment = _environmentService.IsDevelopment
                        };
                        var json = JsonConvert.SerializeObject(input);
                        var functionUrl = _schedulerJoburl;
                        campaign.State = CampaignState.Scheduled;
                        var response = await CallFunctionAsync(input, functionUrl ?? string.Empty);
                        using var jsonDoc = JsonDocument.Parse(response);
                        var scheduledJobId = jsonDoc.RootElement.GetProperty("Id").GetString();
                        if ((dto.Id != null && dto.Id != Guid.Empty))
                        {
                            campaign.Editedby = _userIdentityService.Name;
                            campaign.EditedDate = DateTime.UtcNow;
                            campaign.ScheduleJobId = scheduledJobId;
                            result = await _genericRepository.UpdateRecordAsync<Campaign>("Campaigns", StringHelper.GetPropertyNames<Campaign>(false), campaign, new() { { "CampaignId", dto.Id } });
                        }
                        else
                        {
                            result = await _genericRepository.InsertRecordsAsync("Campaigns", columns, new List<Campaign> { campaign });
                        }
                    }
                    else
                    {
                        await SaveCampaignTrackersAsync(campaign.CampaignId, campaign.BusinessId, campaign.UserId, audienceList ?? new());
                        campaign.State = CampaignState.Scheduled;
                        var input = new
                        {
                            id = campaign.CampaignId,
                            JsonData = JsonConvert.SerializeObject(campaign),
                            type = "Campaign",
                            scheduledTime = DateTime.UtcNow,
                            isDevelopment = _environmentService.IsDevelopment
                        };
                        var json = JsonConvert.SerializeObject(input);
                        var functionUrl = _schedulerJoburl;
                        var response = await CallFunctionAsync(input, functionUrl ?? string.Empty);
                        campaign.DateSetLive = DateTime.UtcNow;
                        using var jsonDoc = JsonDocument.Parse(response);
                        var scheduledJobId = jsonDoc.RootElement.GetProperty("Id").GetString();
                        campaign.ScheduleJobId = scheduledJobId;
                        result = await _genericRepository.InsertRecordsAsync("Campaigns", columns, new List<Campaign> { campaign });
                    }
                    return result;
                }
                throw new Exception("Campaign name already exists.");
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<bool> IsExistCampaignNameAsync(string businessId, string name)
        {
            try
            {
                if (await _genericRepository.IsExistAsync<Campaign>(new Dictionary<string, object> { { "CampaignTitle", name.Trim() }, { "BusinessId", businessId } }, "Campaigns"))
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task SaveCampaignTrackersAsync(Guid campaignId, string businessId, Guid userId, List<string> contactIds)
        {
            try
            {
                if (contactIds?.Any() == true)
                {
                    const int batchSize = 1000;
                    var campaignTrackers = contactIds.Select(id => new CampaignTracker
                    {
                        Id = Guid.NewGuid(),
                        BusinessId = businessId,
                        CampaignId = campaignId,
                        Status = "Pending",
                        UserId = userId,
                        ContactId = id,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = userId,
                        UpdatedAt = DateTime.UtcNow,
                        UpdatedBy = userId
                    });
                    int count = campaignTrackers.Count();
                    // Batching logic
                    for (int i = 0; i < count; i += batchSize)
                    {
                        var batch = campaignTrackers.Skip(i).Take(batchSize).ToList();
                        await _genericRepository.InsertRecordsAsync<CampaignTracker>
                            (
                                nameof(CampaignTracker),
                                StringHelper.GetPropertyNames<CampaignTracker>(),
                                batch
                            );
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception properly (not just throw)
                throw new Exception("Error saving campaign trackers in batch.", ex);
            }
        }


        public async Task<bool> RerunCampaignAsync(Guid userId, string userName, BaseCampaignsDto campaignDto, DateTime? scheduledDate)
        {
            try
            {
                if (!(await IsExistCampaignNameAsync(campaignDto.BusinessId, campaignDto.Name)))
                {
                    var existCampaign = (await _genericRepository.GetByObjectAsync<Campaign>(new Dictionary<string, object> { { "CampaignId", campaignDto.Id ?? Guid.Empty } }, "Campaigns")).FirstOrDefault();
                    if (existCampaign == null || existCampaign.CampaignId == Guid.Empty || existCampaign.State == CampaignState.Incompleted)
                        throw new Exception("Campaign does not exist with the given campaign id.");


                    DateTime scheduledDatetime = (DateTime)(scheduledDate != null ? scheduledDate : DateTime.UtcNow);
                    await RerunCampaignBackgroundAsync(userId, userName, campaignDto, scheduledDate, existCampaign ?? new());
                    return true;
                }
                throw new Exception("Campaign with this name is already exist.");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<bool> RerunCampaignBackgroundAsync(Guid userId, string userName, BaseCampaignsDto campaignDto, DateTime? scheduledDate, Campaign campaign)
        {
            try
            {
                const int batchSize = 1000;
                var newCampaign = campaign.Adapt<Campaign>();
                newCampaign.CampaignId = Guid.NewGuid();
                int totalCount = await _genericRepository.Count<CampaignTracker>(new()
                {
                    { nameof(CampaignTracker.CampaignId), campaign.CampaignId },
                    { nameof(CampaignTracker.ConvStatus), ConvStatus.failed }
                });
                #region Save Campaign 
                for (int i = 0; i < totalCount; i += batchSize)
                {
                    var failedContactIds = await _genericRepository.GetColumnValuesAsync<string>
                     (
                        new()
                        {
                            new(nameof(CampaignTracker.CampaignId), campaign.CampaignId, "="),
                            new(nameof(CampaignTracker.ConvStatus), (int)ConvStatus.failed, "=")
                        },
                        tableName: nameof(CampaignTracker),
                        columnName: nameof(CampaignTracker.ContactId),
                        orderByColumn: nameof(CampaignTracker.CreatedAt),
                        orderDirection: "ASC",
                        page: i,
                        pageSize: batchSize
                     );

                    await SaveCampaignTrackersAsync(newCampaign.CampaignId, campaign.BusinessId, _userIdentityService.UserId, failedContactIds);
                }
                #endregion
                newCampaign.UserId = userId;
                newCampaign.CreatedDate = DateTime.UtcNow;
                newCampaign.CampaignTitle = campaignDto.Name ?? string.Empty;
                newCampaign.State = CampaignState.Scheduled;
                newCampaign.UploadedFileId = 0;
                newCampaign.DateSetLive = (scheduledDate != null && scheduledDate >= DateTime.UtcNow) ? scheduledDate : DateTime.UtcNow;
                var columns = StringHelper.GetPropertyNames<Campaign>(false);
                var input = new
                {
                    id = newCampaign.CampaignId,
                    JsonData = JsonConvert.SerializeObject(newCampaign),
                    type = "Campaign",
                    scheduledTime = newCampaign.DateSetLive,
                    isDevelopment = _environmentService.IsDevelopment
                };
                var functionUrl = _schedulerJoburl;
                newCampaign.State = CampaignState.Scheduled;
                var response = await CallFunctionAsync(input, functionUrl ?? string.Empty);
                using var jsonDoc = JsonDocument.Parse(response);
                var scheduledJobId = jsonDoc.RootElement.GetProperty("Id").GetString();
                newCampaign.ScheduleJobId = scheduledJobId;
                newCampaign.Editedby = userName;
                newCampaign.EditedDate = DateTime.UtcNow;
                var result = await _genericRepository.InsertRecordsAsync("Campaigns", columns, new List<Campaign> { newCampaign });
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        private async Task<string> CallFunctionAsync<T>(T requestBody, string functionUrl)
        {
            if (string.IsNullOrEmpty(functionUrl))
            {
                throw new InvalidOperationException("Function URL is not configured.");
            }
            var client = _httpClientFactory.CreateClient();
            var jsonPayload = System.Text.Json.JsonSerializer.Serialize(requestBody);
            var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
            var response = await client.PostAsync(functionUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            Console.WriteLine(result);
            return result;
        }

        public async Task<bool> DeleteAsync(string businessId, Guid id)
        {
            try
            {
                var campain = await _genericRepository.GetByObjectAsync<Campaign>(new Dictionary<string, object> { { "BusinessId", businessId }, { "CampaignId", id } }, "Campaigns");
                if (campain == null)
                    return true;

                if (!string.IsNullOrEmpty(campain.FirstOrDefault()?.ScheduleJobId))
                {
                    // _jobService.Delete(campain.FirstOrDefault()?.ScheduleJobId ?? "");

                    var functionUrl = _terminateJobUrl;

                    var terminateRequestBody = new { JobId = campain.FirstOrDefault()?.ScheduleJobId ?? "" };
                    await CallFunctionAsync(terminateRequestBody, functionUrl ?? string.Empty);
                }
                return await _genericRepository.DeleteRecordAsync<Campaign>("Campaigns", new List<RequestFilterDto>()
                     {
                       new RequestFilterDto()
                       {
                            Value = businessId,
                            Key = "BusinessId"
                       },
                       new RequestFilterDto()
                       {
                            Value = id,
                            Key = "CampaignId"
                       }
                    });
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<Campaign> GetCampaignAsync(string businessId, Guid campaignId)
        {

            try
            {
                var campain = await _appDbContext.Campaigns.FirstOrDefaultAsync(i => i.BusinessId == businessId && i.CampaignId == campaignId);
                return campain ?? new();
            }
            catch (Exception ex)
            {
                throw;
            }


        }

        public async Task<List<CampaignMessageCountsDto>> GetCampaignAnalyticsAsync(string businessId, Guid? userId, FilterDto? filter, int? page, int? pageSize)
        {
            try
            {
                bool isFilterData = false;
                if (filter == null || filter?.Sorting == null)
                {
                    filter.Sorting = new() { Column = "CreatedDate", Order = "desc" };
                }
                if (filter?.Filtering == null)
                {
                    filter.Filtering = new FilterGroup() { FilterType = "and" };
                    filter.Filtering.Conditions = new() { new() { Column = "State", Operator = "=", Value = ((int)CampaignState.Completed).ToString() } };
                    isFilterData = true;
                }
                var campaigns = await _filterService.FilterDataAsync<Campaign>(businessId, null, filter, "Campaigns", page, pageSize);
                if (isFilterData)
                {
                    campaigns = campaigns.Where(x => x.State == CampaignState.Completed);
                }
                var waIds = campaigns
                    .Where(x => !string.IsNullOrEmpty(x.WhatsAppMessagesId))
                    .GroupBy(x => x.CampaignId)
                    .ToDictionary(
                        group => group.Key,
                        group => group
                            .SelectMany(x => x.WhatsAppMessagesId.Split(','))
                            .Where(id => !string.IsNullOrEmpty(id))
                            .ToList()
                    );

                var campaignAnalytics = await _inboxRepository.GetCampaignAnalyticsCountAsync(businessId, waIds, CancellationToken.None);

                var joinedCampaigns = campaigns.GroupJoin(
                        campaignAnalytics,
                        campaign => campaign.CampaignId,
                        analytics => analytics.Key,
                        (campaign, analyticsGroup) =>
                        {
                            var analyticsValues = analyticsGroup.SelectMany(a => a.Value);
                            var sentCount = analyticsValues.Sum(v => v.AttemptedCount);
                            var deliveredCount = analyticsValues.Sum(v => v.DeliveredCount);
                            var failedCount = analyticsValues.Sum(v => v.FailedCount ?? 0);
                            var repliedCount = analyticsValues.Sum(v => v.RepliedCount ?? 0);
                            var readCount = analyticsValues.Sum(v => v.ReadCount ?? 0);

                            return new CampaignMessageCountsDto
                            {
                                CampaignId = campaign.CampaignId.ToString(),
                                CampaignTitle = campaign.CampaignTitle,
                                Createdby = campaign.Createdby,
                                Attempted = campaign.WhatsAppMessagesId?.Split(",").Length,
                                Scheduled = campaign.Audiance?.Split(",").Length,
                                Sent = sentCount,
                                Delivered = deliveredCount,
                                Failed = failedCount,
                                Replied = repliedCount,
                                Read = readCount,
                                DateSetLive = campaign.DateSetLive,
                                State = campaign.State
                            };
                        }
                    ).ToList();
                joinedCampaigns.ForEach(x => x.Undelivered = x.Sent - (x.Delivered + x.Failed));

                // Apply any extra filtering if needed
                return ExtraFilterData(joinedCampaigns, filter);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<List<ViewCampaignAnalyticsDto>> GetAllCampaignAnalyticsAsync(string businessId, FilterDto? filter, int? page, int? pageSize)
        {
            try
            {

                if (filter == null || filter?.Sorting == null)
                {
                    filter.Sorting = new() { Column = "CreatedDate", Order = "desc" };
                }

                // Ensure Filtering and its Conditions list are initialized
                filter.Filtering ??= new FilterGroup { FilterType = "and", Conditions = new List<FilterCondition>() };

                // Ensure "State = Completed" filter is included

                bool hasStateCondition = filter.Filtering.Conditions!.Any(c =>
                    c.Column.Equals("State", StringComparison.OrdinalIgnoreCase));
                if (!hasStateCondition)
                {
                    filter.Filtering.Conditions?.Add(new FilterCondition
                    {
                        Column = "State",
                        Operator = "=",
                        Value = ((int)CampaignState.Completed).ToString()
                    });
                }
                var campaigns = await _filterService.FilterDataAsync<Campaign>(businessId, null, filter, "Campaigns", page, pageSize);

                var viewCampaignAnalyticsDto = campaigns.Adapt<List<ViewCampaignAnalyticsDto>>();
                return viewCampaignAnalyticsDto;
            }
            catch (Exception ex)
            {
                throw;
            }

        }

        public async Task<CampaignAnalytsicDto> GetCampaignAnalyticsDetailsAsync(Guid CampaignId)
        {
            var businessId = _userIdentityService.BusinessId;
            var campaignAnalytics = await _inboxRepository.GetCampaignAnalyticsCountAsyncById(businessId, CampaignId, CancellationToken.None);
            campaignAnalytics.Undelivered = campaignAnalytics.AttemptedCount - (campaignAnalytics.DeliveredCount + campaignAnalytics.FailedCount);
            return campaignAnalytics;

        }

        public async Task<List<Campaign>> GetScheduleCampaignAsync(string businessId, Guid? userId, FilterDto? filter, int? page, int? pageSize)
        {
            try
            {
                if (filter == null || filter?.Sorting == null)
                {
                    filter.Sorting = new() { Column = "CreatedDate", Order = "desc" };
                }
                if (filter?.Filtering == null)
                {
                    filter.Filtering = new FilterGroup() { FilterType = "and" };
                    filter.Filtering.Conditions = new() { new() { Column = "State", Operator = "=", Value = ((int)CampaignState.Scheduled).ToString() } };
                }
                var campaigns = await _filterService.FilterDataAsync<Campaign>(businessId, null, filter, "Campaigns", page, pageSize);
                return campaigns.ToList();
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<int> GetCampaignCountAsync(string businessId, CampaignState state, FilterCondition? condition, Search? search)
        {
            if (condition != null && !string.IsNullOrEmpty(condition.Value) && DateTime.TryParse(condition.Value, out var date))
            {
                switch (condition.Operator)
                {

                    case "last7days":
                        condition.Operator = ">";
                        condition.Value = date != DateTime.MinValue ? date.AddDays(-7).ToString("o") : DateTime.UtcNow.AddDays(-7).ToString("o");
                        break;
                    case "last14days":
                        condition.Operator = ">";
                        condition.Value = date != DateTime.MinValue ? date.AddDays(-14).ToString("o") : DateTime.UtcNow.AddDays(-14).ToString("o");
                        break;
                    case "last30days":
                        condition.Operator = ">";
                        condition.Value = date != DateTime.MinValue ? date.AddDays(-30).ToString("o") : DateTime.UtcNow.AddDays(-30).ToString("o");
                        break;
                    case "all":
                        condition.Operator = "<";
                        condition.Value = date != DateTime.MinValue ? date.AddDays(1).ToString("o") : DateTime.UtcNow.AddDays(1).ToString("o");
                        break;
                    case "next7days":
                        condition.Operator = "<";
                        condition.Value = date != DateTime.MinValue ? date.AddDays(7).ToString("o") : DateTime.UtcNow.AddDays(7).ToString("o");
                        break;
                    case "next14days":
                        condition.Operator = "<";
                        condition.Value = date != DateTime.MinValue ? date.AddDays(14).ToString("o") : DateTime.UtcNow.AddDays(14).ToString("o");
                        break;
                    case "next30days":
                        condition.Operator = "<";
                        condition.Value = date != DateTime.MinValue ? date.AddDays(30).ToString("o") : DateTime.UtcNow.AddDays(30).ToString("o");
                        break;
                    case "allschedule":
                        condition.Operator = ">";
                        condition.Value = date != DateTime.MinValue ? date.ToString("o") : DateTime.UtcNow.ToString("o");
                        break;

                }
            }

            return await _campaignRespository.GetCampaignCountAsync(businessId, state, condition, search);
        }

        public async Task<DataTable> GetCampaignExportAsync(string businessId, Guid userId, Guid campaignId)
        {
            if (_userIdentityService.BusinessId != businessId && _userIdentityService.UserId != userId)
                throw new UnauthorizedAccessException("Invalid business id or user id");

            // Fetch completed campaigns
            var completedCampaigns = await _campaignDbContext.Campaigns
                .Where(c => c.State == CampaignState.Completed && c.BusinessId == businessId && c.CampaignId == campaignId)
                .ToListAsync();

            if (!completedCampaigns.Any())
                return new DataTable();

            // Extract WhatsAppMessageIds
            var campaignMessageIds = completedCampaigns
                .Where(c => !string.IsNullOrEmpty(c.WhatsAppMessagesId))
                .SelectMany(c => c.WhatsAppMessagesId.Split(',', StringSplitOptions.RemoveEmptyEntries))
                .Distinct()
                .ToList();

            if (!campaignMessageIds.Any())
                return new DataTable();

            // Fetch conversations
            var conversations = await _campaignDbContext.Conversations
                .Where(convo => campaignMessageIds.Contains(convo.WhatsAppMessageId))
                .ToListAsync();

            // Extract contact numbers and concatenate them (client-side)
            var contactNumbers = conversations
                    .Select(c => c.To.Replace("+", ""))
                    .Distinct()
                    .ToList();

            // Fetch contacts from the database (client-side processing of concatenation and distinct)
            var contacts = await _campaignDbContext.Contacts
                .Where(contact => contactNumbers.Contains(string.Concat(contact.CountryCode, contact.Contact).Replace("+", "")))
                .ToListAsync();

            var contactDictionary = contacts
                .DistinctBy(contact => string.Concat(contact.CountryCode, contact.Contact).Replace("+", ""))
                .ToDictionary(contact => string.Concat(contact.CountryCode, contact.Contact).Replace("+", ""), contact => contact);

            // Pre-fetch template names
            var templateNames = await _campaignDbContext.Templates
                .Where(t => completedCampaigns.Select(c => c.TemplateId).Contains(t.TemplateId))
                .ToDictionaryAsync(t => t.TemplateId, t => t.TemplateName);

            // Create the DataTable
            var dataTable = new DataTable();
            dataTable.Columns.Add("Campaign Title");
            dataTable.Columns.Add("CreatedBy");
            dataTable.Columns.Add("Campaign State");
            dataTable.Columns.Add("Campaign LiveDate");
            dataTable.Columns.Add("TemplateName");
            dataTable.Columns.Add("Status");
            dataTable.Columns.Add("MessageType");
            dataTable.Columns.Add("Contact Name");
            dataTable.Columns.Add("Contact Number");
            dataTable.Columns.Add("Error");

            // Populate DataTable
            foreach (var campaign in completedCampaigns)
            {
                var relatedConversations = conversations
                    .Where(convo => !string.IsNullOrEmpty(campaign.WhatsAppMessagesId) && campaign.WhatsAppMessagesId.Contains(convo.WhatsAppMessageId))
                    .ToList();

                var templateName = templateNames.TryGetValue(campaign.TemplateId ?? Guid.Empty, out var name) ? name : "";

                foreach (var conversation in relatedConversations)
                {
                    var contact = contactDictionary.TryGetValue(conversation.To.Replace("+", ""), out var contactDetails) ? contactDetails : null;

                    dataTable.Rows.Add(
                        campaign.CampaignTitle,
                        campaign.Createdby,
                        campaign.State.ToString(),
                        campaign.DateSetLive,
                        templateName,
                        conversation.Status,
                        conversation.MessageType,
                        contact?.Name ?? "",
                        string.Concat(contact?.CountryCode, contact?.Contact) ?? conversation.To,
                        conversation.ErrorMessage
                    );
                }
            }
            return dataTable;
        }

        #region Extra Filter
        private List<CampaignMessageCountsDto> ExtraFilterData(List<CampaignMessageCountsDto> data, FilterDto filter)
        {
            IEnumerable<CampaignMessageCountsDto> result = data;
            if (filter?.Sorting != null && !string.IsNullOrEmpty(filter.Sorting.Column) && !string.IsNullOrEmpty(filter.Sorting.Order))
            {
                string columnName = filter.Sorting.Column.ToLower();
                switch (columnName)
                {
                    case "sentcount":
                        result = filter.Sorting.Order.ToLower() == "desc" ? data.OrderByDescending(c => c.Sent) : data.OrderBy(c => c.Sent);
                        break;
                    case "deliveredcount":
                        result = filter.Sorting.Order.ToLower() == "desc" ? data.OrderByDescending(c => c.Delivered) : data.OrderBy(c => c.Delivered);
                        break;
                    case "readcount":
                        result = filter.Sorting.Order.ToLower() == "desc" ? data.OrderByDescending(c => c.Read) : data.OrderBy(c => c.Read);
                        break;
                    default:
                        break;
                }
            }
            return result.ToList();
        }
        #endregion

        public async Task<FileStreamResult> ExportCampaignByIdAsync(Guid id, CancellationToken cancellationToken)
        {
            var businessId = _userIdentityService.BusinessId;
            var campaignReport = await _inboxRepository.GetCampaignReportByIdAsync(businessId, id, CancellationToken.None);

            var memoryStream = CreateCampaignReportInExcel(campaignReport);

            var fileResult = new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            {
                FileDownloadName = $"CampaignReport-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx"
            };
            return fileResult;
        }

        public MemoryStream CreateCampaignReportInExcel(List<CampaignReportsDto> campaignReports)
        {
            MemoryStream stream = new MemoryStream();
            try
            {
                using (SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook))
                {
                    WorkbookPart workbookPart = spreadsheetDocument.AddWorkbookPart();
                    workbookPart.Workbook = new Workbook();
                    WorksheetPart worksheetPart = workbookPart.AddNewPart<WorksheetPart>();
                    worksheetPart.Worksheet = new Worksheet(new SheetData());

                    Sheets sheets = spreadsheetDocument.WorkbookPart.Workbook.AppendChild(new Sheets());
                    Sheet sheet = new Sheet()
                    {
                        Id = spreadsheetDocument.WorkbookPart.GetIdOfPart(worksheetPart),
                        SheetId = 1,
                        Name = "Campaign Report"
                    };
                    sheets.Append(sheet);

                    Worksheet worksheet = worksheetPart.Worksheet;
                    SheetData sheetData = worksheet.GetFirstChild<SheetData>();

                    List<string> headers = new List<string>
              {
                  "Campaign Title", "Template Name", "Message Type", "Status",
                  "Scheduled Date", "State", "Contact Name", "Phone Number",
                  "Created By", "Error Message"
              };

                    Row headerRow = new Row();
                    foreach (var header in headers)
                    {
                        Cell cell = new Cell()
                        {
                            CellValue = new CellValue(header),
                            DataType = CellValues.String
                        };
                        headerRow.Append(cell);
                    }
                    sheetData.Append(headerRow);

                    foreach (var campaign in campaignReports)
                    {
                        Row row = new Row();
                        row.Append(CreateCell(campaign.Name ?? "N/A"));
                        row.Append(CreateCell(campaign.TemplateName ?? "N/A"));
                        row.Append(CreateCell(campaign.MessageType.ToString() ?? "N/A"));
                        row.Append(CreateCell(campaign.Status.ToString() ?? "N/A"));
                        row.Append(CreateCell(campaign.ScheduleDate?.ToString("yyyy-MM-dd") ?? "N/A"));
                        row.Append(CreateCell(campaign.State.ToString() ?? "N/A"));
                        row.Append(CreateCell(campaign.CustomerName ?? "N/A"));
                        row.Append(CreateCell(campaign.PhoneNumber ?? "N/A"));
                        row.Append(CreateCell(campaign.CreatedBy.ToString() ?? "N/A"));
                        row.Append(CreateCell(campaign.Error ?? "N/A"));
                        sheetData.Append(row);
                    }
                    worksheetPart.Worksheet.Save();
                    workbookPart.Workbook.Save();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }

            stream.Position = 0;
            return stream;
        }
        private static Cell CreateCell(string value)
        {
            return new Cell
            {
                CellValue = new CellValue(value),
                DataType = CellValues.String
            };
        }

        public async Task<CampaignUploadResultDto?> CampaignExcelUploadedFile(UploadFileDto fileDto)
        {
            try
            {
                var companyId = _userIdentityService.BusinessId;

                var wallet = await _walletService.GetWalletAsync(companyId);

                if (wallet?.Balance < 10)
                {
                    return null;
                }

                var totalContacts = await CountContactsInExcelFile(fileDto.File);

                bool canUpload;
                if (wallet.Balance >= 100)
                {

                    canUpload = totalContacts <= (wallet.Balance + 50);
                }
                else
                {
                    canUpload = totalContacts <= (wallet.Balance + 5);
                }

                if (canUpload)
                {
                    var uploadResult = await _blobStorageService.UploadAsync(fileDto);
                    return new CampaignUploadResultDto
                    {
                        UploadResult = uploadResult,
                        ContactCount = totalContacts
                    };
                }

                return new CampaignUploadResultDto
                {
                    UploadResult = null,
                    ContactCount = totalContacts
                };
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error processing campaign Excel upload: {ex.Message}", ex);
            }
        }

        private async Task<int> CountContactsInExcelFile(IFormFile file)
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                using (var stream = file.OpenReadStream())
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets[0];

                    if (worksheet.Dimension == null)
                    {
                        return 0;
                    }

                    var totalRows = worksheet.Dimension.End.Row - 1;

                    return Math.Max(0, totalRows);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error counting contacts in Excel file: {ex.Message}", ex);
            }
        }

 
        public async Task<CampaignContactsResponseDto> GetCampaignContactsByStatusAsync(CampaignContactFilterDto filter)
        {
            try
            {
                // Validate input
                if (filter.PageNumber < 1) filter.PageNumber = 1;
                if (filter.PageSize < 1) filter.PageSize = 10;
                if (filter.PageSize > 100) filter.PageSize = 100; // Limit max page size

                // Step 1: Get conversations for the campaign with the specified status using existing filter method
                var conversationFilters = new List<RequestFilterDto>
                {
                    new RequestFilterDto("ReferenceId", filter.CampaignId.ToString(), "="),
                    new RequestFilterDto("Status", (int)filter.Status, "=")
                };

                var conversations = await _genericRepository.GetRecordByRequestFilter<Conversations>(
                    conversationFilters,
                    "Conversations",
                    filter.PageNumber,
                    filter.PageSize);

                if (!conversations.Any())
                {
                    return new CampaignContactsResponseDto
                    {
                        Contacts = new List<CampaignContactDetailDto>(),
                        TotalCount = 0,
                        PageNumber = filter.PageNumber,
                        PageSize = filter.PageSize
                    };
                }

                // Step 2: Extract unique phone numbers from conversations
                var phoneNumbers = conversations
                    .Select(c => c.To?.Replace("+", ""))
                    .Where(phone => !string.IsNullOrEmpty(phone))
                    .Distinct()
                    .ToList();

                // Step 3: Get contacts that match these phone numbers using existing filter method
                var contactFilters = new List<RequestFilterDto>();

                if (!string.IsNullOrEmpty(filter.BusinessId))
                {
                    contactFilters.Add(new RequestFilterDto("BusinessId", filter.BusinessId, "="));
                }

                // Create OR conditions for phone number matching
                foreach (var phone in phoneNumbers)
                {
                    contactFilters.Add(new RequestFilterDto("Contact", phone, "=") { LogicalOperator = "OR" });
                }

                var contacts = await _genericRepository.GetRecordByRequestFilter<Contacts>(
                    contactFilters,
                    "Contacts");

                // Step 4: Join conversations with contacts and create response
                var contactDetails = new List<CampaignContactDetailDto>();

                foreach (var conversation in conversations)
                {
                    var normalizedConvPhone = conversation.To?.Replace("+", "");
                    var matchingContact = contacts.FirstOrDefault(c =>
                        c.Contact?.Replace("+", "") == normalizedConvPhone);

                    if (matchingContact != null)
                    {
                        contactDetails.Add(new CampaignContactDetailDto
                        {
                            ContactId = matchingContact.ContactId,
                            Name = matchingContact.Name ?? string.Empty,
                            Contact = matchingContact.Contact ?? string.Empty,
                            CountryCode = matchingContact.CountryCode ?? string.Empty,
                            Email = matchingContact.Email,
                            Tags = matchingContact.Tags,
                            ChatStatus = matchingContact.ChatStatus,
                            CreatedDate = matchingContact.CreatedDate,
                            IsActive = matchingContact.IsActive,
                            ConversationStatus = conversation.Status,
                            ConversationCreatedAt = conversation.CreatedAt,
                            WhatsAppMessageId = conversation.WhatsAppMessageId,
                            TextMessage = conversation.TextMessage,
                            ErrorMessage = conversation.ErrorMessage
                        });
                    }
                }

                // Step 5: Get total count for pagination (without pagination to get accurate count)
                var totalConversations = await _genericRepository.GetRecordByRequestFilter<Conversations>(
                    conversationFilters,
                    "Conversations");
                var totalCount = totalConversations.Count;

                // Step 6: Get status summary
                var statusSummary = await GetCampaignStatusSummaryAsync(filter.CampaignId, filter.BusinessId);

                return new CampaignContactsResponseDto
                {
                    Contacts = contactDetails,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize
                };
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error getting campaign contacts by status: {ex.Message}", ex);
            }
        }

        private async Task<CampaignStatusSummaryDto> GetCampaignStatusSummaryAsync(Guid campaignId, string? businessId)
        {
            try
            {
                var sql = @"
                    SELECT
                        conv.Status,
                        COUNT(*) as Count
                    FROM Conversations conv
                    INNER JOIN Contacts c ON REPLACE(conv.[To], '+', '') = REPLACE(c.CountryCode + c.Contact, '+', '')
                    WHERE conv.ReferenceId = @CampaignId
                    AND (@BusinessId IS NULL OR c.BusinessId = @BusinessId)
                    GROUP BY conv.Status";

                var parameters = new
                {
                    CampaignId = campaignId.ToString(),
                    BusinessId = businessId
                };

                using var connection = _connectionFactory.CreateConnection();
                var results = await connection.QueryAsync<dynamic>(sql, parameters);

                var summary = new CampaignStatusSummaryDto();
                foreach (var row in results)
                {
                    var status = (ConvStatus)row.Status;
                    var count = (int)row.Count;

                    switch (status)
                    {
                        case ConvStatus.sent:
                            summary.SentCount = count;
                            break;
                        case ConvStatus.delivered:
                            summary.DeliveredCount = count;
                            break;
                        case ConvStatus.read:
                            summary.ReadCount = count;
                            break;
                        case ConvStatus.failed:
                            summary.FailedCount = count;
                            break;
                    }
                }

                return summary;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error getting campaign status summary: {ex.Message}", ex);
            }
        }

        public async Task<CampaignContactsResponseDto> GetCampaignContactsByStatusStoredProcAsync(CampaignContactFilterDto filter)
        {
            try
            {
                // Validate input
                if (filter.PageNumber < 1) filter.PageNumber = 1;
                if (filter.PageSize < 1) filter.PageSize = 10;
                if (filter.PageSize > 100) filter.PageSize = 100; 

                var parameters = new
                {
                    CampaignId = filter.CampaignId,
                    Status = (int)filter.Status,
                    BusinessId = filter.BusinessId,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize
                };

                using var connection = _connectionFactory.CreateConnection();

                // Execute stored procedure with multiple result sets
                using var multi = await connection.QueryMultipleAsync(
                    "GetCampaignContactsDashBoard",
                    parameters,
                    commandType: CommandType.StoredProcedure);

                // First result set: paginated contacts
                var contactResults = await multi.ReadAsync<dynamic>();
                var contacts = new List<CampaignContactDetailDto>();
                var totalCount = 0;

                foreach (var row in contactResults)
                {
                    totalCount = row.TotalCount;
                    contacts.Add(new CampaignContactDetailDto
                    {
                        ContactId = row.ContactId,
                        Name = row.Name ?? string.Empty,
                        Contact = row.Contact ?? string.Empty,
                        CountryCode = row.CountryCode ?? string.Empty,
                        Email = row.Email,
                        Tags = row.Tags,
                        ChatStatus = (EngagetoEntities.Enums.ChatStatus)row.ChatStatus,
                        CreatedDate = row.CreatedDate,
                        IsActive = row.IsActive,
                        ConversationStatus = (ConvStatus)row.ConversationStatus,
                        ConversationCreatedAt = row.ConversationCreatedAt,
                        WhatsAppMessageId = row.WhatsAppMessageId,
                        TextMessage = row.TextMessage,
                        ErrorMessage = row.ErrorMessage
                    });
                }

                // Second result set: status summary
                var statusResults = await multi.ReadAsync<dynamic>();
                var statusSummary = new CampaignStatusSummaryDto();

                foreach (var row in statusResults)
                {
                    var status = (ConvStatus)row.Status;
                    var count = (int)row.Count;

                    switch (status)
                    {
                        case ConvStatus.sent:
                            statusSummary.SentCount = count;
                            break;
                        case ConvStatus.delivered:
                            statusSummary.DeliveredCount = count;
                            break;
                        case ConvStatus.read:
                            statusSummary.ReadCount = count;
                            break;
                        case ConvStatus.failed:
                            statusSummary.FailedCount = count;
                            break;
                    }
                }

                return new CampaignContactsResponseDto
                {
                    Contacts = contacts,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize
                };
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error getting campaign contacts by status using stored procedure: {ex.Message}", ex);
            }
        }
    }
}