using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.CampaignDto
{
    /// <summary>
    /// DTO for campaign contact statistics including attempted, delivered, read, failed, and reply counts
    /// </summary>
    public class CampaignContactStatsDto
    {
        /// <summary>
        /// Campaign ID
        /// </summary>
        public Guid CampaignId { get; set; }

        /// <summary>
        /// Campaign title/name
        /// </summary>
        public string CampaignTitle { get; set; } = string.Empty;

        /// <summary>
        /// Total number of contacts attempted (all contacts in campaign)
        /// </summary>
        public int AttemptedCount { get; set; }

        /// <summary>
        /// Number of messages delivered (ConvStatus.delivered)
        /// </summary>
        public int DeliveredCount { get; set; }

        /// <summary>
        /// Number of messages read (ConvStatus.read)
        /// </summary>
        public int ReadCount { get; set; }

        /// <summary>
        /// Number of messages failed (ConvStatus.failed)
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// Number of replies received (conversations with ReplyId matching campaign WhatsApp message IDs)
        /// </summary>
        public int ReplyCount { get; set; }

        /// <summary>
        /// Number of messages sent (ConvStatus.sent)
        /// </summary>
        public int SentCount { get; set; }

        /// <summary>
        /// Campaign creation date
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Campaign creator
        /// </summary>
        public string? CreatedBy { get; set; }
    }
}
