using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.CampaignDto
{
    public class CampaignContactFilterDto
    {
        public Guid CampaignId { get; set; }
        public ConvStatus Status { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? BusinessId { get; set; }
    }
    public class CampaignContactsResponseDto
    {
        /// <summary>
        /// Paginated list of contacts
        /// </summary>
        public List<CampaignContactDetailDto> Contacts { get; set; } = new();

        /// <summary>
        /// Total count of all records matching the filter
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Current page number
        /// </summary>
        public int PageNumber { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Total number of pages
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    }

    /// <summary>
    /// Contact details with conversation information
    /// </summary>
    public class CampaignContactDetailDto
    {
        public Guid ContactId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Contact { get; set; } = string.Empty;
        public string CountryCode { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? Tags { get; set; }
        public ChatStatus ChatStatus { get; set; }
        public DateTime? CreatedDate { get; set; }
        public bool IsActive { get; set; }

        // Conversation details
        public ConvStatus ConversationStatus { get; set; }
        public DateTime ConversationCreatedAt { get; set; }
        public string? WhatsAppMessageId { get; set; }
        public string? TextMessage { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Summary counts for all statuses
    /// </summary>
    public class CampaignStatusSummaryDto
    {
        public int SentCount { get; set; }
        public int DeliveredCount { get; set; }
        public int ReadCount { get; set; }
        public int FailedCount { get; set; }
    }
}
