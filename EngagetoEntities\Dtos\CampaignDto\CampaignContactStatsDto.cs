using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.CampaignDto
{
    /// <summary>
    /// DTO for campaign contact statistics including attempted, delivered, read, failed, and reply counts
    /// </summary>
    public class CampaignContactStatsDto
    {
        /// <summary>
        /// Campaign ID
        /// </summary>
        public Guid CampaignId { get; set; }

        /// <summary>
        /// Campaign title/name
        /// </summary>
        public string CampaignTitle { get; set; } = string.Empty;

        /// <summary>
        /// Total number of contacts attempted (all contacts in campaign)
        /// </summary>
        public int AttemptedCount { get; set; }

        /// <summary>
        /// Number of messages delivered (ConvStatus.delivered)
        /// </summary>
        public int DeliveredCount { get; set; }

        /// <summary>
        /// Number of messages read (ConvStatus.read)
        /// </summary>
        public int ReadCount { get; set; }

        /// <summary>
        /// Number of messages failed (ConvStatus.failed)
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// Number of replies received (conversations with ReplyId matching campaign WhatsApp message IDs)
        /// </summary>
        public int ReplyCount { get; set; }

        /// <summary>
        /// Number of messages sent (ConvStatus.sent)
        /// </summary>
        public int SentCount { get; set; }

        /// <summary>
        /// Campaign creation date
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Campaign creator
        /// </summary>
        public string? CreatedBy { get; set; }
    }

    /// <summary>
    /// DTO for filtering campaign contacts by status with pagination
    /// </summary>
    public class CampaignContactFilterDto
    {
        /// <summary>
        /// Campaign ID to filter by
        /// </summary>
        public Guid CampaignId { get; set; }

        /// <summary>
        /// Conversation status to filter by (sent, delivered, read, failed)
        /// </summary>
        public ConvStatus Status { get; set; }

        /// <summary>
        /// Page number for pagination (1-based)
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Number of records per page
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// Business ID for security filtering
        /// </summary>
        public string? BusinessId { get; set; }
    }

    /// <summary>
    /// Response DTO for paginated campaign contacts
    /// </summary>
    public class CampaignContactsResponseDto
    {
        /// <summary>
        /// Paginated list of contacts
        /// </summary>
        public List<CampaignContactDetailDto> Contacts { get; set; } = new();

        /// <summary>
        /// Total count of all records matching the filter
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Current page number
        /// </summary>
        public int PageNumber { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Total number of pages
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

        /// <summary>
        /// Summary counts of each status (optional)
        /// </summary>
        public CampaignStatusSummaryDto? StatusSummary { get; set; }
    }

    /// <summary>
    /// Contact details with conversation information
    /// </summary>
    public class CampaignContactDetailDto
    {
        public Guid ContactId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Contact { get; set; } = string.Empty;
        public string CountryCode { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? Tags { get; set; }
        public ChatStatus ChatStatus { get; set; }
        public DateTime? CreatedDate { get; set; }
        public bool IsActive { get; set; }

        // Conversation details
        public ConvStatus ConversationStatus { get; set; }
        public DateTime ConversationCreatedAt { get; set; }
        public string? WhatsAppMessageId { get; set; }
        public string? TextMessage { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Summary counts for all statuses
    /// </summary>
    public class CampaignStatusSummaryDto
    {
        public int SentCount { get; set; }
        public int DeliveredCount { get; set; }
        public int ReadCount { get; set; }
        public int FailedCount { get; set; }
    }
}
