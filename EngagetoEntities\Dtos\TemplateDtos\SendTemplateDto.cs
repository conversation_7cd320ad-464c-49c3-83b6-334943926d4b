﻿using EngagetoEntities.Dtos.CommonDtos.PaymentGatwayDtos;

namespace EngagetoEntities.Dtos.TemplateDtos
{
    /// <summary>
    /// DTO for sending templates with support for both normal variables ({{1}}) and LeadRat variables (#Name#)
    /// </summary>
    public class SendTemplateDto
    {
        public string BusinessId { get; set; }
        public Guid UserId { get; set; }
        public Guid TemplateId { get; set; }
        public List<string> Contact { get; set; }
        public VariableDto? HeaderVariableValue { get; set; }
        public List<VariableDto>? BodyVariableValues { get; set; }
        public string[]? RedirectUrlVariableValues { get; set; }
        public string? MadiaUrl { get; set; }
        public string? MediaFile { get; set; }
    }

    /// <summary>
    /// Variable DTO supporting both types of variables:
    /// 1. Normal variables ({{1}}): Use Value field to pass contact property name (e.g., "Name"), system fetches from contact database
    /// 2. LeadRat variables (#Name#): Use Variable field to specify variable (e.g., "#leadName#"), system calls LeadRat API to fetch value
    /// FallbackValue is used when the primary value cannot be retrieved
    /// </summary>
    public class VariableDto
    {
        /// <summary>
        /// For LeadRat variables: specify the variable name (e.g., "#leadName#", "#propertyName#")
        /// For normal variables: leave empty or null
        /// </summary>
        public string? Variable { get; set; } = default!;

        /// <summary>
        /// For normal variables: specify contact property name (e.g., "Name", "Contact", "Email")
        /// For LeadRat variables: leave empty, value will be fetched from LeadRat API
        /// </summary>
        public string? Value { get; set; }

        /// <summary>
        /// Fallback value used when primary value cannot be retrieved
        /// </summary>
        public string? FallbackValue { get; set; } = default!;
    }
}
