﻿using EngagetoEntities.Dtos.CommonDtos.PaymentGatwayDtos;

namespace EngagetoEntities.Dtos.TemplateDtos
{
    public class SendTemplateDto
    {
        public string BusinessId { get; set; }
        public Guid UserId { get; set; }
        public Guid TemplateId { get; set; }
        public List<string> Contact { get; set; }
        public VariableDto? HeaderVariableValue { get; set; }
        public List<VariableDto>? BodyVariableValues { get; set; }
        public string[]? RedirectUrlVariableValues { get; set; }
        public string? MadiaUrl { get; set; }
        public string? MediaFile { get; set; }
    }

    public class VariableDto
    {
        public string? Value { get; set; }
        public string? FallbackValue { get; set; }
    }
}
