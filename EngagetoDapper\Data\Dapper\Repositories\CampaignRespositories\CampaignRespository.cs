﻿using Dapper;
using DocumentFormat.OpenXml.Spreadsheet;
using EngagetoContracts.AutomationContracts;
using EngagetoDapper.Data.Connections;
using EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignRepositories;
using EngagetoEntities.Dtos.CampaignDto;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.FilterDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Dtos;
using System.Data;
using System.Globalization;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;


namespace EngagetoDapper.Data.Dapper.Repositories.CampaignRespositories
{
    public class CampaignRespository : ICampaignRespository
    {
        private readonly ISqlConnectionFactory _connectionFactory;
        public CampaignRespository(ISqlConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<IEnumerable<ConverstationDetailsDto>> GetConversationDetailsAsync(string companyId)
        {
            string sql = "SELECT * FROM GetCompanySubscriptionAndContDetails(@CompanyId)";
            var parameters = new { CompanyId = companyId };
            using var connection = _connectionFactory.CreateConnection();
            var result = await connection.QueryAsync<ConverstationDetailsDto>(sql, parameters);
            return result;

        }
        public async Task<int> GetCampaignCountAsync(string companyId, CampaignState state, FilterCondition? condition,Search? search)
        {
            TextInfo textInfo = CultureInfo.CurrentCulture.TextInfo;
            
            string sql = @"
                SELECT COUNT(*) 
                FROM Campaigns 
                WHERE BusinessId = @BusinessId
                AND State = @State";

            var parameters = new DynamicParameters();
            parameters.Add("BusinessId", companyId);
            parameters.Add("State", state);

            if (condition != null && !string.IsNullOrEmpty(condition.Column) && !string.IsNullOrEmpty(condition.Operator))
            {
                if (DateTime.TryParse(condition.Value, out var date))
                {
                    sql += $" AND {textInfo.ToTitleCase(condition.Column)} {condition.Operator} @ConditionValue";
                    parameters.Add("ConditionValue", date);
                }
                else
                {
                    sql += $" AND {textInfo.ToTitleCase(condition.Column)} {condition.Operator} @ConditionValue";
                    parameters.Add("ConditionValue", condition.Value);
                }
            }
            if (search != null && !string.IsNullOrEmpty(search.Column) && !string.IsNullOrEmpty(search.Value))
            {
                sql += $" AND {textInfo.ToTitleCase(search.Column)} LIKE @SearchValue";
                parameters.Add("SearchValue", $"%{search.Value}%"); 
            }
            using var connection = _connectionFactory.CreateConnection();
            var result = await connection.ExecuteScalarAsync<int>(sql, parameters);
            return result;
        }

        public async Task<bool> BulkUpdateCampaignTrackerAsync(List<CampaignTracker> updates)
        {
            // 1. Create and fill DataTable that matches the TVP structure
            var table = new DataTable();
            table.Columns.Add(nameof(CampaignTracker.BusinessId), typeof(string));
            table.Columns.Add(nameof(CampaignTracker.CampaignId), typeof(Guid));
            table.Columns.Add(nameof(CampaignTracker.ContactId), typeof(string));
            table.Columns.Add(nameof(CampaignTracker.Status), typeof(string));
            table.Columns.Add(nameof(CampaignTracker.ConvStatus), typeof(int));
            table.Columns.Add(nameof(CampaignTracker.WhatsAppMessagesId), typeof(string));
            table.Columns.Add(nameof(CampaignTracker.ErrorMessage), typeof(string));

            foreach (var u in updates)
            {
                table.Rows.Add(u.BusinessId,u.CampaignId, u.ContactId, u.Status,u.ConvStatus,
                    u.WhatsAppMessagesId ?? (object)DBNull.Value,
                    u.ErrorMessage ?? (object)DBNull.Value);
            }

            // 2. Call the stored procedure with Dapper
            using var connection = _connectionFactory.CreateConnection();

            var parameters = new DynamicParameters();
            parameters.Add("@Updates", table.AsTableValuedParameter("dbo.CampaignTrackerUpdateType"));

            await connection.ExecuteAsync(
                "dbo.BulkUpdateCampaignTrackers",
                parameters,
                commandType: CommandType.StoredProcedure);

            return true;
        }
        public async Task<List<CampaignContactDetailDto>> GetContactsFromCampaign(CampaignContactFilterDto  campaignContactFilterDto)
        {
            try
            {
                var param = new DynamicParameters();
                param.Add("@BusinessId", campaignContactFilterDto.BusinessId);
                param.Add("@CampaignId", campaignContactFilterDto.CampaignId);
                param.Add("@Status",campaignContactFilterDto.Status);
                param.Add("@PageNumber", campaignContactFilterDto.PageNumber);
                param.Add("@PageSize", campaignContactFilterDto.PageSize);

                using var connection = _connectionFactory.CreateConnection();
                var response = await connection.QueryFirstAsync<List<CampaignContactDetailDto>>(
                    "GetContactsByCampaignStatus",
                    param,
                    commandType: CommandType.StoredProcedure);
                return response;
            }
            catch (Exception ex)
            {
                throw;
            }

        }
    }
}
